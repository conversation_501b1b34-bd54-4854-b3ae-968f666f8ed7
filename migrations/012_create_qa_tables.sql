-- =====================================================
-- 问答社区系统 - 数据库迁移脚本
-- 创建时间: 2025-11-01
-- 说明: 创建问题表、答案表、问题关注表
-- =====================================================

-- 1. 创建问题表
CREATE TABLE IF NOT EXISTS public.questions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    category VARCHAR(50),
    tags VARCHAR(200),
    status VARCHAR(20) DEFAULT 'pending',
    best_answer_id INTEGER,
    view_count INTEGER DEFAULT 0,
    answer_count INTEGER DEFAULT 0,
    follow_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- 2. 创建答案表
CREATE TABLE IF NOT EXISTS public.answers (
    id SERIAL PRIMARY KEY,
    question_id INTEGER NOT NULL REFERENCES public.questions(id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    is_best BOOLEAN DEFAULT FALSE,
    like_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- 3. 创建问题关注表
CREATE TABLE IF NOT EXISTS public.question_follows (
    id SERIAL PRIMARY KEY,
    question_id INTEGER NOT NULL REFERENCES public.questions(id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(question_id, user_id)
);

-- 4. 创建索引
-- 问题表索引
CREATE INDEX IF NOT EXISTS idx_questions_user_id ON public.questions(user_id);
CREATE INDEX IF NOT EXISTS idx_questions_category ON public.questions(category);
CREATE INDEX IF NOT EXISTS idx_questions_status ON public.questions(status);
CREATE INDEX IF NOT EXISTS idx_questions_created_at ON public.questions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_questions_deleted_at ON public.questions(deleted_at);

-- 答案表索引
CREATE INDEX IF NOT EXISTS idx_answers_question_id ON public.answers(question_id);
CREATE INDEX IF NOT EXISTS idx_answers_user_id ON public.answers(user_id);
CREATE INDEX IF NOT EXISTS idx_answers_is_best ON public.answers(is_best);
CREATE INDEX IF NOT EXISTS idx_answers_created_at ON public.answers(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_answers_deleted_at ON public.answers(deleted_at);

-- 问题关注表索引
CREATE INDEX IF NOT EXISTS idx_question_follows_question_id ON public.question_follows(question_id);
CREATE INDEX IF NOT EXISTS idx_question_follows_user_id ON public.question_follows(user_id);

-- 5. 添加外键约束（最佳答案）
ALTER TABLE public.questions 
ADD CONSTRAINT fk_questions_best_answer 
FOREIGN KEY (best_answer_id) 
REFERENCES public.answers(id) 
ON DELETE SET NULL;

-- 6. 创建更新时间触发器函数（如果不存在）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 7. 为问题表创建更新时间触发器
DROP TRIGGER IF EXISTS update_questions_updated_at ON public.questions;
CREATE TRIGGER update_questions_updated_at
BEFORE UPDATE ON public.questions
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 8. 为答案表创建更新时间触发器
DROP TRIGGER IF EXISTS update_answers_updated_at ON public.answers;
CREATE TRIGGER update_answers_updated_at
BEFORE UPDATE ON public.answers
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 9. 创建答案计数触发器函数
CREATE OR REPLACE FUNCTION update_question_answer_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.questions 
        SET answer_count = answer_count + 1 
        WHERE id = NEW.question_id;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.questions 
        SET answer_count = GREATEST(answer_count - 1, 0) 
        WHERE id = OLD.question_id;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 10. 创建答案计数触发器
DROP TRIGGER IF EXISTS trigger_update_answer_count ON public.answers;
CREATE TRIGGER trigger_update_answer_count
AFTER INSERT OR DELETE ON public.answers
FOR EACH ROW
EXECUTE FUNCTION update_question_answer_count();

-- 11. 创建关注计数触发器函数
CREATE OR REPLACE FUNCTION update_question_follow_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.questions 
        SET follow_count = follow_count + 1 
        WHERE id = NEW.question_id;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.questions 
        SET follow_count = GREATEST(follow_count - 1, 0) 
        WHERE id = OLD.question_id;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 12. 创建关注计数触发器
DROP TRIGGER IF EXISTS trigger_update_follow_count ON public.question_follows;
CREATE TRIGGER trigger_update_follow_count
AFTER INSERT OR DELETE ON public.question_follows
FOR EACH ROW
EXECUTE FUNCTION update_question_follow_count();

-- 13. 添加注释
COMMENT ON TABLE public.questions IS '问题表';
COMMENT ON TABLE public.answers IS '答案表';
COMMENT ON TABLE public.question_follows IS '问题关注表';

COMMENT ON COLUMN public.questions.status IS '问题状态: pending-待解决, solved-已解决';
COMMENT ON COLUMN public.questions.best_answer_id IS '最佳答案ID';
COMMENT ON COLUMN public.answers.is_best IS '是否为最佳答案';

-- 14. 验证表创建
SELECT 
    table_name,
    (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count
FROM information_schema.tables t
WHERE table_schema = 'public' 
  AND table_name IN ('questions', 'answers', 'question_follows')
ORDER BY table_name;

