@echo off
echo ========================================
echo Redis 快速启动脚本 (Docker方式)
echo ========================================
echo.

REM 设置Redis密码
set REDIS_PASSWORD=ssq198123

REM 检查Docker是否安装
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Docker未安装或未启动
    echo.
    echo 请选择以下方式之一:
    echo 1. 安装Docker Desktop: https://www.docker.com/products/docker-desktop
    echo 2. 手动安装Redis: https://github.com/tporadowski/redis/releases
    echo.
    pause
    exit /b 1
)

echo [1/3] 检查是否已有Redis容器运行...
docker ps -a --filter "name=redis-heritage" --format "{{.Names}}" | findstr "redis-heritage" >nul 2>&1

if %errorlevel% equ 0 (
    echo [发现] 已存在redis-heritage容器
    echo [2/3] 启动容器...
    docker start redis-heritage
) else (
    echo [2/3] 创建并启动新的Redis容器...
    docker run -d --name redis-heritage -p 6379:6379 --restart unless-stopped ^
        -e REDIS_PASSWORD=%REDIS_PASSWORD% ^
        redis:7-alpine redis-server --requirepass %REDIS_PASSWORD%
)

if %errorlevel% neq 0 (
    echo [错误] Redis启动失败
    pause
    exit /b 1
)

echo [3/3] 测试Redis连接...
timeout /t 2 /nobreak >nul
docker exec redis-heritage redis-cli -a %REDIS_PASSWORD% ping

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo ✅ Redis启动成功！
    echo ========================================
    echo.
    echo Redis地址: localhost:6379
    echo 密码: %REDIS_PASSWORD%
    echo.
    echo 常用命令:
    echo   查看日志: docker logs redis-heritage
    echo   停止服务: docker stop redis-heritage
    echo   删除容器: docker rm redis-heritage
    echo   进入CLI:  docker exec -it redis-heritage redis-cli -a %REDIS_PASSWORD%
    echo.
) else (
    echo [错误] Redis连接测试失败
    echo 请检查容器状态: docker logs redis-heritage
)

pause