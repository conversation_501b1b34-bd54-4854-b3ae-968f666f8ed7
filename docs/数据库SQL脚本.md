# 🗄️ 数据库SQL脚本

> **最后更新：2025-10-28**
> **数据库：PostgreSQL**
> **状态：生产环境可用**

---

## 📋 目录

- [表结构定义](#表结构定义)
- [索引优化](#索引优化)
- [测试数据](#测试数据)
- [数据库迁移记录](#数据库迁移记录)

---

## 表结构定义

### 1. 用户表 (users)

```sql
-- 用户表users
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'user', -- 'user', 'non_heritage', 'admin'
    avatar_url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**字段说明：**
- `id`: 用户唯一标识
- `username`: 用户名（唯一）
- `email`: 邮箱（唯一）
- `password_hash`: 密码哈希值（bcrypt加密）
- `role`: 用户角色（user普通用户/non_heritage非遗人/admin管理员）
- `avatar_url`: 头像URL
- `created_at`: 创建时间
- `updated_at`: 更新时间

---

### 2. 非遗项目表 (heritage_items)

```sql
-- 非遗项目表
CREATE TABLE heritage_items (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    category VARCHAR(50),
    location VARCHAR(100),
    latitude DECIMAL(10, 6),
    longitude DECIMAL(10, 6),
    cover_image VARCHAR(255),
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'approved', 'rejected'
    created_by BIGINT REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**字段说明：**
- `id`: 项目唯一标识
- `name`: 项目名称
- `description`: 项目描述
- `category`: 项目分类（表演艺术/传统戏剧/传统技艺/民俗/传统医药等）
- `location`: 地理位置描述
- `latitude`: 纬度
- `longitude`: 经度
- `cover_image`: 封面图片URL
- `status`: 审核状态（pending待审核/approved已通过/rejected已拒绝）
- `created_by`: 创建者用户ID
- `created_at`: 创建时间

---

### 3. 地理位置表 (locations)

```sql
-- 地理位置表
CREATE TABLE locations (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    address VARCHAR(255),
    latitude DECIMAL(10, 6) NOT NULL,
    longitude DECIMAL(10, 6) NOT NULL,
    type VARCHAR(50), -- 'scenic', 'cultural', 'museum'
    item_id BIGINT REFERENCES heritage_items(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**字段说明：**
- `id`: 位置唯一标识
- `name`: 地点名称
- `address`: 详细地址
- `latitude`: 纬度（必填）
- `longitude`: 经度（必填）
- `type`: 地点类型（scenic景点/cultural文化场所/museum博物馆）
- `item_id`: 关联的非遗项目ID
- `created_at`: 创建时间

---

### 4. 打卡记录表 (checkin_records)

```sql
-- 打卡记录表
CREATE TABLE checkin_records (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    location_id BIGINT REFERENCES locations(id),
    checkin_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    images TEXT, -- 图片URL，多个用逗号分隔
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**字段说明：**
- `id`: 打卡记录唯一标识
- `user_id`: 打卡用户ID
- `location_id`: 打卡地点ID
- `checkin_time`: 打卡时间
- `notes`: 打卡备注
- `images`: 图片URL（多个用逗号分隔）
- `created_at`: 创建时间

**注意：** `images` 字段已从 `TEXT[]` 改为 `TEXT` 类型

---

### 5. UGC内容表 (ugc_contents)

```sql
-- UGC内容表
CREATE TABLE ugc_contents (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    title VARCHAR(200),
    content TEXT,
    type VARCHAR(20), -- 'article', 'photo', 'video'
    media_urls TEXT, -- 媒体URL，多个用逗号分隔
    location_id BIGINT REFERENCES locations(id),
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**字段说明：**
- `id`: 内容唯一标识
- `user_id`: 创建者用户ID
- `title`: 内容标题
- `content`: 内容正文
- `type`: 内容类型（article文章/photo图片/video视频）
- `media_urls`: 媒体URL（多个用逗号分隔）
- `location_id`: 关联地点ID
- `status`: 审核状态（pending待审核/approved已通过/rejected已拒绝）
- `created_at`: 创建时间

**注意：** `media_urls` 字段已从 `TEXT[]` 改为 `TEXT` 类型

---

### 6. 评论表 (comments)

```sql
-- 评论表
CREATE TABLE comments (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) NOT NULL,
    content TEXT NOT NULL,
    target_type VARCHAR(20) NOT NULL,
    target_id BIGINT NOT NULL,
    parent_id BIGINT, -- 允许NULL，不设置外键约束
    like_count INT DEFAULT 0,
    status VARCHAR(20) DEFAULT 'approved',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**字段说明：**
- `id`: 评论唯一标识
- `user_id`: 评论者用户ID
- `content`: 评论内容
- `target_type`: 评论目标类型（ugc_content/heritage_item/comment等）
- `target_id`: 评论目标ID
- `parent_id`: 父评论ID（用于回复，允许NULL）
- `like_count`: 点赞数
- `status`: 审核状态
- `created_at`: 创建时间

---

### 7. 点赞表 (likes)

```sql
-- 点赞表
CREATE TABLE likes (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) NOT NULL,
    target_type VARCHAR(20) NOT NULL,
    target_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, target_type, target_id)
);
```

**字段说明：**
- `id`: 点赞记录唯一标识
- `user_id`: 点赞用户ID
- `target_type`: 点赞目标类型（ugc_content/comment/heritage_item等）
- `target_id`: 点赞目标ID
- `created_at`: 创建时间
- **唯一约束：** 同一用户对同一目标只能点赞一次

---

### 8. 收藏表 (collections)

```sql
-- 收藏表
CREATE TABLE collections (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) NOT NULL,
    target_type VARCHAR(20) NOT NULL,
    target_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, target_type, target_id)
);
```

**字段说明：**
- `id`: 收藏记录唯一标识
- `user_id`: 收藏用户ID
- `target_type`: 收藏目标类型（ugc_content/heritage_item等）
- `target_id`: 收藏目标ID
- `created_at`: 创建时间
- **唯一约束：** 同一用户对同一目标只能收藏一次

---

### 9. 文件记录表 (file_records)

```sql
-- 文件记录表
CREATE TABLE file_records (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    filename VARCHAR(255) NOT NULL,
    file_url VARCHAR(500) NOT NULL,
    file_size BIGINT,
    file_type VARCHAR(50),
    upload_type VARCHAR(20), -- 'avatar', 'ugc', 'checkin'
    target_id BIGINT, -- 关联的目标ID
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**字段说明：**
- `id`: 文件记录唯一标识
- `user_id`: 上传用户ID
- `filename`: 文件名
- `file_url`: 文件URL
- `file_size`: 文件大小（字节）
- `file_type`: 文件MIME类型
- `upload_type`: 上传类型（avatar头像/ugc内容/checkin打卡）
- `target_id`: 关联的目标ID
- `created_at`: 创建时间

---

### 10. 用户关系表 (user_relationships)

```sql
-- 用户关注关系表
CREATE TABLE user_relationships (
    id BIGSERIAL PRIMARY KEY,
    follower_id BIGINT REFERENCES users(id) NOT NULL, -- 关注者
    following_id BIGINT REFERENCES users(id) NOT NULL, -- 被关注者
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(follower_id, following_id)
);
```

**字段说明：**
- `id`: 关系记录唯一标识
- `follower_id`: 关注者用户ID
- `following_id`: 被关注者用户ID
- `created_at`: 创建时间
- **唯一约束：** 同一用户对同一用户只能关注一次

---

## 索引优化

---

## 测试数据

### 1. 插入用户数据

```sql
INSERT INTO users (username, email, password_hash, role, avatar_url) VALUES
('admin', '<EMAIL>', 'hashed_password_123', 'admin', '/avatars/admin.jpg'),
('cultural_lover', '<EMAIL>', 'hashed_password_456', 'user', '/avatars/lover.jpg'),
('travel_enthusiast', '<EMAIL>', 'hashed_password_789', 'user', '/avatars/traveler.jpg'),
('heritage_guardian', '<EMAIL>', 'hashed_password_101', 'non_heritage', '/avatars/guardian.jpg'),
('history_explorer', '<EMAIL>', 'hashed_password_202', 'user', '/avatars/explorer.jpg');
```

---

### 2. 插入非遗项目数据

```sql
INSERT INTO heritage_items (name, description, category, location, latitude, longitude, cover_image, status, created_by) VALUES
('昆曲', '中国最古老的剧种之一，被誉为"百戏之祖"，发源于苏州昆山', '表演艺术', '江苏苏州', 31.298886, 120.585289, '/covers/kunqu.jpg', 'approved', 1),
('皮影戏', '中国民间古老的传统艺术，用灯光照射兽皮或纸板做成的人物剪影表演故事', '传统戏剧', '陕西华县', 34.506207, 109.763291, '/covers/shadowplay.jpg', 'approved', 2),
('景德镇瓷器', '以"白如玉、明如镜、薄如纸、声如磬"闻名世界的传统手工制瓷技艺', '传统技艺', '江西景德镇', 29.268835, 117.178894, '/covers/porcelain.jpg', 'approved', 1),
('端午节', '纪念屈原的传统节日，有赛龙舟、吃粽子等习俗', '民俗', '湖南汨罗', 28.806416, 113.073593, '/covers/dragonboat.jpg', 'approved', 3),
('中医针灸', '以经络理论为基础，通过刺激特定穴位治疗疾病的传统医学', '传统医药', '北京', 39.904202, 116.407394, '/covers/acupuncture.jpg', 'pending', 4);
```

---

### 3. 插入地理位置数据

```sql
INSERT INTO locations (name, address, latitude, longitude, type, item_id) VALUES
('苏州昆剧院', '江苏省苏州市姑苏区观前街138号', 31.308886, 120.625289, 'cultural', 1),
('华县皮影文化园', '陕西省渭南市华县莲花路28号', 34.516207, 109.773291, 'museum', 2),
('景德镇古窑民俗博览区', '江西省景德镇市昌江区瓷都大道古窑路1号', 29.278835, 117.188894, 'scenic', 3),
('汨罗江龙舟竞渡中心', '湖南省岳阳市汨罗市屈原大道', 28.816416, 113.083593, 'cultural', 4),
('北京中医药大学针灸推拿学院', '北京市朝阳区北三环东路11号', 39.914202, 116.417394, 'museum', 5);
```

---

### 4. 插入打卡记录数据

```sql
INSERT INTO checkin_records (user_id, location_id, notes, images) VALUES
(2, 1, '第一次现场观看昆曲表演，太震撼了！演员的唱腔和身段都美极了', '/checkins/kunqu1.jpg,/checkins/kunqu2.jpg'),
(3, 2, '皮影戏的幕后制作过程很有趣，老艺人的手艺真棒', '/checkins/shadow1.jpg'),
(4, 3, '亲手体验了制陶过程，看似简单实则很难，敬佩工匠精神', '/checkins/porcelain1.jpg,/checkins/porcelain2.jpg'),
(2, 4, '端午节来看龙舟赛，气氛热烈，传统文化魅力无限', '/checkins/dragon1.jpg'),
(5, 5, '学习了基础针灸知识，中医文化博大精深', '/checkins/acu1.jpg');
```

---

### 5. 插入UGC内容数据

```sql
INSERT INTO ugc_contents (user_id, title, content, type, media_urls, location_id, status) VALUES
(2, '昆曲之美：一场穿越时空的艺术之旅', '今天在苏州昆剧院欣赏了经典剧目《牡丹亭》，杜丽娘的婉转唱腔让人如痴如醉...', 'article', '/articles/kunqu1.jpg,/articles/kunqu2.jpg', 1, 'approved'),
(3, '皮影戏：光影中的千年传奇', '记录华县皮影戏的制作和表演全过程，老艺人坚守传统技艺令人感动...', 'photo', '/photos/shadow1.jpg,/photos/shadow2.jpg,/photos/shadow3.jpg', 2, 'approved'),
(4, '景德镇制瓷体验日记', '从揉泥、拉坯到绘画、上釉，完整记录传统瓷器制作过程...', 'article', '/articles/porcelain1.jpg', 3, 'approved'),
(5, '端午龙舟赛的激情瞬间', '用镜头捕捉龙舟竞渡的精彩时刻，传统文化在现代焕发新生...', 'video', '/videos/dragon1.mp4', 4, 'approved'),
(2, '针灸文化的现代价值', '探讨传统针灸技艺在现代医疗中的应用和发展前景...', 'article', '/articles/acu1.jpg', 5, 'pending');
```

---

### 6. 插入评论数据

```sql
INSERT INTO comments (user_id, content, target_type, target_id, parent_id, like_count) VALUES
(3, '写得真好！我也想去苏州看昆曲表演了', 'ugc_content', 1, NULL, 5),
(4, '皮影戏的照片拍得太棒了！光影效果很有意境', 'ugc_content', 2, NULL, 3),
(5, '制瓷过程记录得很详细，学到了很多知识', 'ugc_content', 3, NULL, 2),
(2, '谢谢你的分享！龙舟赛确实很震撼', 'ugc_content', 4, NULL, 4),
(3, '中医针灸确实很有价值，希望更多人了解', 'ugc_content', 5, NULL, 1),
(4, '同感！昆曲的服装和妆容也很有讲究', 'comment', 1, 1, 2);
```

---

### 7. 插入点赞数据

```sql
INSERT INTO likes (user_id, target_type, target_id) VALUES
(2, 'ugc_content', 1),
(3, 'ugc_content', 1),
(4, 'ugc_content', 1),
(2, 'ugc_content', 2),
(5, 'ugc_content', 3),
(3, 'comment', 1),
(4, 'comment', 2);
```

---

### 8. 插入收藏数据

```sql
INSERT INTO collections (user_id, target_type, target_id) VALUES
(2, 'ugc_content', 2),
(3, 'ugc_content', 1),
(4, 'ugc_content', 3),
(5, 'ugc_content', 1),
(2, 'heritage_item', 1),
(3, 'heritage_item', 2);
```

---

### 9. 插入文件记录数据

```sql
INSERT INTO file_records (user_id, filename, file_url, file_size, file_type, upload_type, target_id) VALUES
(2, 'kunqu_performance.jpg', '/ugc/kunqu1.jpg', 2048576, 'image/jpeg', 'ugc', 1),
(3, 'shadow_play.jpg', '/ugc/shadow1.jpg', 1567890, 'image/jpeg', 'ugc', 2),
(2, 'avatar_update.png', '/avatars/user2_new.png', 567890, 'image/png', 'avatar', 2),
(4, 'porcelain_making.mp4', '/ugc/porcelain_video.mp4', 15678900, 'video/mp4', 'ugc', 3);
```

---

### 10. 插入用户关注关系数据

```sql
INSERT INTO user_relationships (follower_id, following_id) VALUES
(2, 1),  -- cultural_lover 关注 admin
(3, 2),  -- travel_enthusiast 关注 cultural_lover
(4, 2),  -- heritage_guardian 关注 cultural_lover
(5, 3),  -- history_explorer 关注 travel_enthusiast
(2, 3),  -- cultural_lover 关注 travel_enthusiast
(3, 4);  -- travel_enthusiast 关注 heritage_guardian
```

---


### 评论表索引

```sql
-- 创建索引提高查询性能
CREATE INDEX idx_comments_target ON comments(target_type, target_id);
CREATE INDEX idx_comments_parent ON comments(parent_id);
CREATE INDEX idx_comments_user ON comments(user_id);
```

### 点赞表索引

```sql
CREATE INDEX idx_likes_target ON likes(target_type, target_id);
CREATE INDEX idx_likes_user ON likes(user_id);
```

### 收藏表索引

```sql
CREATE INDEX idx_collections_user ON collections(user_id);
```

### 文件记录表索引

```sql
CREATE INDEX idx_file_records_user ON file_records(user_id);
CREATE INDEX idx_file_records_upload_type ON file_records(upload_type, target_id);
CREATE INDEX idx_file_records_created ON file_records(created_at);
```

### 用户关系表索引

```sql
CREATE INDEX idx_relationships_follower ON user_relationships(follower_id);
CREATE INDEX idx_relationships_following ON user_relationships(following_id);
CREATE INDEX idx_relationships_created ON user_relationships(created_at);
```

---

## 数据库迁移记录

### 迁移1: 修改 checkin_records.images 字段类型

```sql
-- 将 images 字段从 TEXT[] 改为 TEXT 类型
ALTER TABLE checkin_records
ALTER COLUMN images TYPE TEXT;
```

**原因：** 简化数据存储，使用逗号分隔的字符串存储多个图片URL

---

### 迁移2: 添加 checkin_records.created_at 字段

```sql
-- 为 checkin_records 表添加 created_at 字段
ALTER TABLE checkin_records
ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
```

**原因：** 统一所有表的时间戳字段

---

### 迁移3: 修改 ugc_contents.media_urls 字段类型

```sql
ALTER TABLE ugc_contents
ALTER COLUMN media_urls TYPE TEXT;
```

**原因：** 与 checkin_records.images 保持一致

---

### 迁移4: 批量更新 UGC 内容状态

```sql
-- 将所有UGC内容的状态更新为 approved
UPDATE ugc_contents
SET status = 'approved'
WHERE status IS NULL OR status != 'approved';
```

**原因：** 初始化测试数据状态

---

### 迁移5: 修复 comments 表外键约束

```sql
-- 删除现有的comments表
DROP TABLE IF EXISTS comments;

-- 重新创建comments表，parent_id允许NULL
CREATE TABLE comments (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) NOT NULL,
    content TEXT NOT NULL,
    target_type VARCHAR(20) NOT NULL,
    target_id BIGINT NOT NULL,
    parent_id BIGINT, -- 允许NULL，不设置外键约束
    like_count INT DEFAULT 0,
    status VARCHAR(20) DEFAULT 'approved',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 只创建必要的索引
CREATE INDEX idx_comments_target ON comments(target_type, target_id);
CREATE INDEX idx_comments_parent ON comments(parent_id);
CREATE INDEX idx_comments_user ON comments(user_id);
```

**原因：** parent_id 需要允许 NULL，且不设置外键约束以避免循环依赖

---

## 📊 数据库统计

- **已实现表：** 10张
- **索引数量：** 13个
- **外键约束：** 9个
- **唯一约束：** 5个

---

## 🔄 下次更新计划

### 待添加的表

1. **heritage_person_applications** - 非遗人认证申请表（🔴 高优先级）
2. **roles** - 角色定义表（🔴 高优先级）
3. **permissions** - 权限定义表（🔴 高优先级）
4. **role_permissions** - 角色权限关联表（🔴 高优先级）
5. **questions** - 问答-问题表（🟡 中优先级）
6. **answers** - 问答-回答表（🟡 中优先级）
7. **achievements** - 成就定义表（🟡 中优先级）
8. **user_achievements** - 用户成就表（🟡 中优先级）
9. **points_records** - 积分记录表（🟡 中优先级）

---

## 📝 使用说明

1. **初始化数据库：** 按顺序执行"表结构定义"部分的SQL
2. **创建索引：** 执行"索引优化"部分的SQL
3. **插入测试数据：** 执行下一节的测试数据SQL
4. **数据迁移：** 如果数据库已存在，执行"数据库迁移记录"中的相关SQL

---

**文档维护者：** AI开发助手
**最后更新：** 2025-10-28

-- 非遗人认证申请表
CREATE TABLE heritage_person_applications (
id BIGSERIAL PRIMARY KEY,
user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
real_name VARCHAR(50) NOT NULL,
id_card VARCHAR(18) NOT NULL,
heritage_category VARCHAR(50) NOT NULL,
heritage_item_id BIGINT REFERENCES heritage_items(id) ON DELETE SET NULL,
certification_level VARCHAR(20) NOT NULL,
certification_number VARCHAR(100),
certification_files TEXT,
expertise_description TEXT,
application_status VARCHAR(20) DEFAULT 'pending' CHECK (application_status IN ('pending', 'approved', 'rejected')),
applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
reviewed_at TIMESTAMP,
reviewer_id BIGINT REFERENCES users(id) ON DELETE SET NULL,
review_comment TEXT,
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_heritage_person_applications_user_id ON heritage_person_applications(user_id);
CREATE INDEX idx_heritage_person_applications_status ON heritage_person_applications(application_status);
CREATE INDEX idx_heritage_person_applications_applied_at ON heritage_person_applications(applied_at);

-- 添加注释
COMMENT ON TABLE heritage_person_applications IS '非遗人认证申请表';
COMMENT ON COLUMN heritage_person_applications.id IS '申请ID';
COMMENT ON COLUMN heritage_person_applications.user_id IS '申请用户ID';
COMMENT ON COLUMN heritage_person_applications.real_name IS '真实姓名';
COMMENT ON COLUMN heritage_person_applications.id_card IS '身份证号';
COMMENT ON COLUMN heritage_person_applications.heritage_category IS '非遗类别';
COMMENT ON COLUMN heritage_person_applications.heritage_item_id IS '关联的非遗项目ID';
COMMENT ON COLUMN heritage_person_applications.certification_level IS '认证级别（国家级/省级/市级）';
COMMENT ON COLUMN heritage_person_applications.certification_number IS '认证证书编号';
COMMENT ON COLUMN heritage_person_applications.certification_files IS '认证材料文件路径（多个用逗号分隔）';
COMMENT ON COLUMN heritage_person_applications.expertise_description IS '专业技能描述';
COMMENT ON COLUMN heritage_person_applications.application_status IS '申请状态（pending/approved/rejected）';
COMMENT ON COLUMN heritage_person_applications.applied_at IS '申请时间';
COMMENT ON COLUMN heritage_person_applications.reviewed_at IS '审核时间';
COMMENT ON COLUMN heritage_person_applications.reviewer_id IS '审核人ID';
COMMENT ON COLUMN heritage_person_applications.review_comment IS '审核意见';
COMMENT ON COLUMN heritage_person_applications.created_at IS '创建时间';
COMMENT ON COLUMN heritage_person_applications.updated_at IS '更新时间';


-- ============================================
-- RBAC权限系统 - 数据库表结构
-- ============================================

-- 1. 角色表 (roles)
CREATE TABLE roles (
id BIGSERIAL PRIMARY KEY,
name VARCHAR(50) UNIQUE NOT NULL,
display_name VARCHAR(100) NOT NULL,
description TEXT,
is_system BOOLEAN DEFAULT FALSE, -- 是否为系统内置角色
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. 权限表 (permissions)
CREATE TABLE permissions (
id BIGSERIAL PRIMARY KEY,
name VARCHAR(100) UNIQUE NOT NULL,
display_name VARCHAR(100) NOT NULL,
description TEXT,
resource VARCHAR(50) NOT NULL, -- 资源类型：user, heritage_item, ugc_content等
action VARCHAR(50) NOT NULL,   -- 操作类型：create, read, update, delete
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. 角色权限关联表 (role_permissions)
CREATE TABLE role_permissions (
id BIGSERIAL PRIMARY KEY,
role_id BIGINT NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
permission_id BIGINT NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
UNIQUE(role_id, permission_id)
);

-- 4. 用户角色关联表 (user_roles) - 支持多角色
CREATE TABLE user_roles (
id BIGSERIAL PRIMARY KEY,
user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
role_id BIGINT NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
UNIQUE(user_id, role_id)
);

-- ============================================
-- 创建索引
-- ============================================

CREATE INDEX idx_role_permissions_role ON role_permissions(role_id);
CREATE INDEX idx_role_permissions_permission ON role_permissions(permission_id);
CREATE INDEX idx_user_roles_user ON user_roles(user_id);
CREATE INDEX idx_user_roles_role ON user_roles(role_id);
CREATE INDEX idx_permissions_resource ON permissions(resource);

-- ============================================
-- 插入系统默认角色
-- ============================================

INSERT INTO roles (name, display_name, description, is_system) VALUES
('admin', '系统管理员', '拥有所有权限的超级管理员', TRUE),
('non_heritage', '非遗传承人', '经过认证的非遗传承人，可以发布和管理非遗内容', TRUE),
('user', '普通用户', '注册用户，可以浏览、打卡、评论、点赞等', TRUE),
('guest', '游客', '未登录用户，只能浏览公开内容', TRUE);

-- ============================================
-- 插入系统默认权限
-- ============================================

-- 用户管理权限
INSERT INTO permissions (name, display_name, description, resource, action) VALUES
('user.create', '创建用户', '创建新用户账号', 'user', 'create'),
('user.read', '查看用户', '查看用户信息', 'user', 'read'),
('user.update', '更新用户', '更新用户信息', 'user', 'update'),
('user.delete', '删除用户', '删除用户账号', 'user', 'delete'),
('user.manage_role', '管理用户角色', '分配和修改用户角色', 'user', 'manage_role');

-- 非遗项目权限
INSERT INTO permissions (name, display_name, description, resource, action) VALUES
('heritage_item.create', '创建非遗项目', '创建新的非遗项目', 'heritage_item', 'create'),
('heritage_item.read', '查看非遗项目', '查看非遗项目详情', 'heritage_item', 'read'),
('heritage_item.update', '更新非遗项目', '更新非遗项目信息', 'heritage_item', 'update'),
('heritage_item.delete', '删除非遗项目', '删除非遗项目', 'heritage_item', 'delete'),
('heritage_item.approve', '审核非遗项目', '审核非遗项目的发布申请', 'heritage_item', 'approve');

-- UGC内容权限
INSERT INTO permissions (name, display_name, description, resource, action) VALUES
('ugc_content.create', '创建UGC内容', '发布用户生成内容', 'ugc_content', 'create'),
('ugc_content.read', '查看UGC内容', '查看用户生成内容', 'ugc_content', 'read'),
('ugc_content.update', '更新UGC内容', '更新自己的UGC内容', 'ugc_content', 'update'),
('ugc_content.delete', '删除UGC内容', '删除自己的UGC内容', 'ugc_content', 'delete'),
('ugc_content.approve', '审核UGC内容', '审核用户生成内容', 'ugc_content', 'approve');

-- 评论权限
INSERT INTO permissions (name, display_name, description, resource, action) VALUES
('comment.create', '发表评论', '发表评论', 'comment', 'create'),
('comment.read', '查看评论', '查看评论', 'comment', 'read'),
('comment.update', '更新评论', '更新自己的评论', 'comment', 'update'),
('comment.delete', '删除评论', '删除评论', 'comment', 'delete'),
('comment.manage', '管理评论', '管理所有评论（管理员）', 'comment', 'manage');

-- 非遗人认证权限
INSERT INTO permissions (name, display_name, description, resource, action) VALUES
('heritage_person.apply', '申请认证', '申请非遗传承人认证', 'heritage_person', 'apply'),
('heritage_person.review', '审核认证', '审核非遗传承人认证申请', 'heritage_person', 'review');

-- 角色权限管理
INSERT INTO permissions (name, display_name, description, resource, action) VALUES
('role.create', '创建角色', '创建新角色', 'role', 'create'),
('role.read', '查看角色', '查看角色信息', 'role', 'read'),
('role.update', '更新角色', '更新角色信息', 'role', 'update'),
('role.delete', '删除角色', '删除角色', 'role', 'delete'),
('permission.read', '查看权限', '查看权限列表', 'permission', 'read'),
('permission.assign', '分配权限', '为角色分配权限', 'permission', 'assign');

-- ============================================
-- 为系统角色分配权限
-- ============================================

-- 管理员拥有所有权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT
(SELECT id FROM roles WHERE name = 'admin'),
id
FROM permissions;

-- 非遗传承人权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT
(SELECT id FROM roles WHERE name = 'non_heritage'),
id
FROM permissions
WHERE name IN (
'heritage_item.create',
'heritage_item.read',
'heritage_item.update',
'ugc_content.create',
'ugc_content.read',
'ugc_content.update',
'ugc_content.delete',
'comment.create',
'comment.read',
'comment.update',
'comment.delete',
'user.read'
);

-- 普通用户权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT
(SELECT id FROM roles WHERE name = 'user'),
id
FROM permissions
WHERE name IN (
'heritage_item.read',
'ugc_content.create',
'ugc_content.read',
'ugc_content.update',
'ugc_content.delete',
'comment.create',
'comment.read',
'comment.update',
'comment.delete',
'heritage_person.apply',
'user.read'
);

-- 游客权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT
(SELECT id FROM roles WHERE name = 'guest'),
id
FROM permissions
WHERE name IN (
'heritage_item.read',
'ugc_content.read',
'comment.read'
);

-- ============================================
-- 为现有用户分配角色
-- ============================================

-- 为所有现有用户根据其role字段分配对应的角色
INSERT INTO user_roles (user_id, role_id)
SELECT
u.id,
r.id
FROM users u
JOIN roles r ON r.name = u.role
WHERE NOT EXISTS (
SELECT 1 FROM user_roles ur
WHERE ur.user_id = u.id AND ur.role_id = r.id
);

-- ============================================
-- 添加注释
-- ============================================

COMMENT ON TABLE roles IS '角色表';
COMMENT ON TABLE permissions IS '权限表';
COMMENT ON TABLE role_permissions IS '角色权限关联表';
COMMENT ON TABLE user_roles IS '用户角色关联表';

COMMENT ON COLUMN roles.is_system IS '是否为系统内置角色（不可删除）';
COMMENT ON COLUMN permissions.resource IS '资源类型';
COMMENT ON COLUMN permissions.action IS '操作类型';


-- 1. 启用PostGIS扩展
CREATE EXTENSION IF NOT EXISTS postgis;

-- 2. 验证PostGIS安装
SELECT PostGIS_Version();


-- 步骤3: 添加geography字段（用于高精度地理计算）
-- GEOGRAPHY类型使用球面坐标，适合全球范围的距离计算
ALTER TABLE public.locations
ADD COLUMN IF NOT EXISTS geog GEOGRAPHY(POINT, 4326);

-- 步骤4: 添加注释
COMMENT ON COLUMN public.locations.geog IS 'PostGIS地理坐标字段，用于高精度距离计算';

-- 步骤5: 从现有的latitude和longitude字段迁移数据到geog字段
-- ST_MakePoint(经度, 纬度) - 注意顺序！
UPDATE public.locations
SET geog = ST_SetSRID(ST_MakePoint(longitude, latitude), 4326)::geography
WHERE latitude IS NOT NULL
AND longitude IS NOT NULL
AND geog IS NULL;

-- 步骤6: 创建空间索引（GIST索引，大幅提升地理查询性能）
CREATE INDEX IF NOT EXISTS idx_locations_geog
ON public.locations USING GIST(geog);

-- 步骤7: 创建其他常用索引
CREATE INDEX IF NOT EXISTS idx_locations_type
ON public.locations(type);

CREATE INDEX IF NOT EXISTS idx_locations_item_id
ON public.locations(item_id);

CREATE INDEX IF NOT EXISTS idx_locations_created_at
ON public.locations(created_at DESC);

-- 步骤8: 创建触发器函数，自动同步latitude/longitude到geog字段
CREATE OR REPLACE FUNCTION sync_location_geog()
RETURNS TRIGGER AS $$
BEGIN
-- 当latitude或longitude更新时，自动更新geog字段
IF (NEW.latitude IS NOT NULL AND NEW.longitude IS NOT NULL) THEN
NEW.geog := ST_SetSRID(ST_MakePoint(NEW.longitude, NEW.latitude), 4326)::geography;
END IF;
RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 步骤9: 创建触发器
DROP TRIGGER IF EXISTS trigger_sync_location_geog ON public.locations;
CREATE TRIGGER trigger_sync_location_geog
BEFORE INSERT OR UPDATE OF latitude, longitude
ON public.locations
FOR EACH ROW
EXECUTE FUNCTION sync_location_geog();

-- ==========================================
-- 验证和测试
-- ==========================================

-- 验证1: 查看表结构
SELECT
column_name,
data_type,
udt_name,
is_nullable
FROM information_schema.columns
WHERE table_name = 'locations'
ORDER BY ordinal_position;

-- 验证2: 查看前5条数据的地理坐标
SELECT
id,
name,
latitude,
longitude,
ST_AsText(geog::geometry) as geog_wkt,
ST_Y(geog::geometry) as geog_latitude,
ST_X(geog::geometry) as geog_longitude
FROM public.locations
WHERE geog IS NOT NULL
LIMIT 5;

-- 验证3: 统计数据迁移情况
SELECT
COUNT(*) as total_locations,
COUNT(geog) as locations_with_geog,
COUNT(*) - COUNT(geog) as locations_without_geog,
ROUND(COUNT(geog)::numeric / NULLIF(COUNT(*), 0) * 100, 2) as migration_percentage
FROM public.locations;

-- 验证4: 查看索引
SELECT
indexname,
indexdef
FROM pg_indexes
WHERE tablename = 'locations'
ORDER BY indexname;

-- 验证5: 测试地理搜索功能（查找距离某点5公里内的位置）
-- 示例：查找距离北京天安门（39.9042, 116.4074）5公里内的位置
SELECT
id,
name,
address,
latitude,
longitude,
ROUND(
ST_Distance(
geog,
ST_SetSRID(ST_MakePoint(116.4074, 39.9042), 4326)::geography
)::numeric,
2
) as distance_meters
FROM public.locations
WHERE ST_DWithin(
geog,
ST_SetSRID(ST_MakePoint(116.4074, 39.9042), 4326)::geography,
5000  -- 5000米 = 5公里
)
ORDER BY distance_meters ASC
LIMIT 10;

-- ==========================================
-- 性能测试
-- ==========================================

-- 测试1: 不使用索引的查询（慢）
EXPLAIN ANALYZE
SELECT COUNT(*)
FROM public.locations
WHERE latitude BETWEEN 39.0 AND 40.0
AND longitude BETWEEN 116.0 AND 117.0;

-- 测试2: 使用PostGIS空间索引的查询（快）
EXPLAIN ANALYZE
SELECT COUNT(*)
FROM public.locations
WHERE ST_DWithin(
geog,
ST_SetSRID(ST_MakePoint(116.4074, 39.9042), 4326)::geography,
50000  -- 50公里
);

-- ==========================================
-- 常用查询示例
-- ==========================================

-- 示例1: 查找附近的位置（按距离排序）
-- 参数: 经度116.4074, 纬度39.9042, 半径5000米
SELECT
id,
name,
address,
type,
ROUND(ST_Distance(
geog,
ST_SetSRID(ST_MakePoint(116.4074, 39.9042), 4326)::geography
)::numeric, 2) as distance_meters
FROM public.locations
WHERE ST_DWithin(
geog,
ST_SetSRID(ST_MakePoint(116.4074, 39.9042), 4326)::geography,
5000
)
ORDER BY distance_meters ASC;

-- 示例2: 查找某个矩形区域内的位置
SELECT
id,
name,
latitude,
longitude
FROM public.locations
WHERE ST_Within(
geog::geometry,
ST_MakeEnvelope(116.0, 39.0, 117.0, 40.0, 4326)
);

-- 示例3: 计算两个位置之间的距离
SELECT
l1.name as location1,
l2.name as location2,
ROUND(ST_Distance(l1.geog, l2.geog)::numeric, 2) as distance_meters
FROM public.locations l1
CROSS JOIN public.locations l2
WHERE l1.id = 1 AND l2.id = 2;


-- 步骤1：检查当前状态
SELECT
COUNT(*) as total_locations,
COUNT(geog) as locations_with_geog,
COUNT(*) - COUNT(geog) as locations_without_geog
FROM public.locations;

-- 步骤2：更新所有geog字段为NULL的记录
UPDATE public.locations
SET geog = ST_SetSRID(ST_MakePoint(longitude, latitude), 4326)::geography
WHERE geog IS NULL
AND latitude IS NOT NULL
AND longitude IS NOT NULL;

-- 步骤3：验证更新结果
SELECT
COUNT(*) as total_locations,
COUNT(geog) as locations_with_geog,
COUNT(*) - COUNT(geog) as locations_without_geog,
ROUND(COUNT(geog)::numeric / NULLIF(COUNT(*), 0) * 100, 2) as migration_percentage
FROM public.locations;

-- 步骤4：查看前5条记录验证
SELECT
id,
name,
latitude,
longitude,
geog IS NOT NULL as has_geog,
ST_AsText(geog::geometry) as geog_text
FROM public.locations
LIMIT 5;

-- 步骤5：测试PostGIS查询（查询天安门附近5公里的位置）
SELECT
id,
name,
latitude,
longitude,
ROUND(
ST_Distance(
geog,
ST_SetSRID(ST_MakePoint(116.4074, 39.9042), 4326)::geography
)::numeric,
2
) as distance_meters
FROM public.locations
WHERE geog IS NOT NULL
AND ST_DWithin(
geog,
ST_SetSRID(ST_MakePoint(116.4074, 39.9042), 4326)::geography,
5000
)
ORDER BY distance_meters ASC
LIMIT 10;

-- 创建问题表
CREATE TABLE questions (
id BIGSERIAL PRIMARY KEY,
user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
title VARCHAR(200) NOT NULL,
content TEXT NOT NULL,
category VARCHAR(50),
tags VARCHAR(200),
status VARCHAR(20) DEFAULT 'pending',
best_answer_id BIGINT,
view_count INTEGER DEFAULT 0,
answer_count INTEGER DEFAULT 0,
follow_count INTEGER DEFAULT 0,
like_count INTEGER DEFAULT 0,
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
deleted_at TIMESTAMP
);

-- 创建答案表
CREATE TABLE answers (
id BIGSERIAL PRIMARY KEY,
question_id BIGINT NOT NULL REFERENCES questions(id) ON DELETE CASCADE,
user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
content TEXT NOT NULL,
is_best BOOLEAN DEFAULT FALSE,
like_count INTEGER DEFAULT 0,
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
deleted_at TIMESTAMP
);

-- 创建问题关注表
CREATE TABLE question_follows (
id BIGSERIAL PRIMARY KEY,
question_id BIGINT NOT NULL REFERENCES questions(id) ON DELETE CASCADE,
user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
UNIQUE(question_id, user_id)
);


-- 添加外键约束（最佳答案）
ALTER TABLE questions
ADD CONSTRAINT fk_questions_best_answer
FOREIGN KEY (best_answer_id)
REFERENCES answers(id)
ON DELETE SET NULL;

-- 问题表索引
CREATE INDEX idx_questions_user_id ON questions(user_id);
CREATE INDEX idx_questions_category ON questions(category);
CREATE INDEX idx_questions_status ON questions(status);
CREATE INDEX idx_questions_created_at ON questions(created_at DESC);
CREATE INDEX idx_questions_deleted_at ON questions(deleted_at);

-- 答案表索引
CREATE INDEX idx_answers_question_id ON answers(question_id);
CREATE INDEX idx_answers_user_id ON answers(user_id);
CREATE INDEX idx_answers_is_best ON answers(is_best);
CREATE INDEX idx_answers_created_at ON answers(created_at DESC);
CREATE INDEX idx_answers_deleted_at ON answers(deleted_at);

-- 问题关注表索引
CREATE INDEX idx_question_follows_question_id ON question_follows(question_id);
CREATE INDEX idx_question_follows_user_id ON question_follows(user_id);

-- 创建更新时间触发器函数（如果不存在）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
NEW.updated_at = CURRENT_TIMESTAMP;
RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为问题表创建更新时间触发器
DROP TRIGGER IF EXISTS update_questions_updated_at ON questions;
CREATE TRIGGER update_questions_updated_at
BEFORE UPDATE ON questions
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 为答案表创建更新时间触发器
DROP TRIGGER IF EXISTS update_answers_updated_at ON answers;
CREATE TRIGGER update_answers_updated_at
BEFORE UPDATE ON answers
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();







-- 创建答案计数触发器函数
CREATE OR REPLACE FUNCTION update_question_answer_count()
RETURNS TRIGGER AS $$
BEGIN
IF TG_OP = 'INSERT' THEN
UPDATE questions
SET answer_count = answer_count + 1
WHERE id = NEW.question_id;
ELSIF TG_OP = 'DELETE' THEN
UPDATE questions
SET answer_count = GREATEST(answer_count - 1, 0)
WHERE id = OLD.question_id;
END IF;
RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 创建答案计数触发器
DROP TRIGGER IF EXISTS trigger_update_answer_count ON answers;
CREATE TRIGGER trigger_update_answer_count
AFTER INSERT OR DELETE ON answers
FOR EACH ROW
EXECUTE FUNCTION update_question_answer_count();



-- 创建关注计数触发器函数
CREATE OR REPLACE FUNCTION update_question_follow_count()
RETURNS TRIGGER AS $$
BEGIN
IF TG_OP = 'INSERT' THEN
UPDATE questions
SET follow_count = follow_count + 1
WHERE id = NEW.question_id;
ELSIF TG_OP = 'DELETE' THEN
UPDATE questions
SET follow_count = GREATEST(follow_count - 1, 0)
WHERE id = OLD.question_id;
END IF;
RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 创建关注计数触发器
DROP TRIGGER IF EXISTS trigger_update_follow_count ON question_follows;
CREATE TRIGGER trigger_update_follow_count
AFTER INSERT OR DELETE ON question_follows
FOR EACH ROW
EXECUTE FUNCTION update_question_follow_count();


-- 添加注释
COMMENT ON TABLE questions IS '问题表';
COMMENT ON TABLE answers IS '答案表';
COMMENT ON TABLE question_follows IS '问题关注表';

COMMENT ON COLUMN questions.status IS '问题状态: pending-待解决, solved-已解决';
COMMENT ON COLUMN questions.best_answer_id IS '最佳答案ID';
COMMENT ON COLUMN answers.is_best IS '是否为最佳答案';



-- 1. 创建积分规则表
CREATE TABLE point_rules (
id BIGSERIAL PRIMARY KEY,
action_type VARCHAR(50) UNIQUE NOT NULL,
action_name VARCHAR(100) NOT NULL,
points INTEGER NOT NULL,
description TEXT,
daily_limit INTEGER DEFAULT 0,
is_active BOOLEAN DEFAULT TRUE,
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. 创建积分记录表
CREATE TABLE point_records (
id BIGSERIAL PRIMARY KEY,
user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
action_type VARCHAR(50) NOT NULL,
points INTEGER NOT NULL,
description TEXT,
reference_type VARCHAR(50),
reference_id BIGINT,
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. 创建成就表
CREATE TABLE achievements (
id BIGSERIAL PRIMARY KEY,
code VARCHAR(50) UNIQUE NOT NULL,
name VARCHAR(100) NOT NULL,
description TEXT,
icon_url VARCHAR(255),
category VARCHAR(50),
condition_type VARCHAR(50) NOT NULL,
condition_value INTEGER NOT NULL,
reward_points INTEGER DEFAULT 0,
is_active BOOLEAN DEFAULT TRUE,
display_order INTEGER DEFAULT 0,
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. 创建用户成就表
CREATE TABLE user_achievements (
id BIGSERIAL PRIMARY KEY,
user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
achievement_id BIGINT NOT NULL REFERENCES achievements(id) ON DELETE CASCADE,
progress INTEGER DEFAULT 0,
is_completed BOOLEAN DEFAULT FALSE,
completed_at TIMESTAMP,
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
UNIQUE(user_id, achievement_id)
);

-- 5. 创建用户等级表（扩展users表的等级信息）
CREATE TABLE user_levels (
id BIGSERIAL PRIMARY KEY,
user_id BIGINT UNIQUE NOT NULL REFERENCES users(id) ON DELETE CASCADE,
level INTEGER DEFAULT 1,
total_points INTEGER DEFAULT 0,
current_level_points INTEGER DEFAULT 0,
next_level_points INTEGER DEFAULT 100,
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 6. 创建索引
-- 积分规则表索引
CREATE INDEX idx_point_rules_action_type ON point_rules(action_type);
CREATE INDEX idx_point_rules_is_active ON point_rules(is_active);

-- 积分记录表索引
CREATE INDEX idx_point_records_user_id ON point_records(user_id);
CREATE INDEX idx_point_records_action_type ON point_records(action_type);
CREATE INDEX idx_point_records_created_at ON point_records(created_at DESC);
CREATE INDEX idx_point_records_reference ON point_records(reference_type, reference_id);

-- 成就表索引
CREATE INDEX idx_achievements_code ON achievements(code);
CREATE INDEX idx_achievements_category ON achievements(category);
CREATE INDEX idx_achievements_is_active ON achievements(is_active);
CREATE INDEX idx_achievements_display_order ON achievements(display_order);

-- 用户成就表索引
CREATE INDEX idx_user_achievements_user_id ON user_achievements(user_id);
CREATE INDEX idx_user_achievements_achievement_id ON user_achievements(achievement_id);
CREATE INDEX idx_user_achievements_is_completed ON user_achievements(is_completed);
CREATE INDEX idx_user_achievements_completed_at ON user_achievements(completed_at DESC);

-- 用户等级表索引
CREATE INDEX idx_user_levels_user_id ON user_levels(user_id);
CREATE INDEX idx_user_levels_level ON user_levels(level DESC);
CREATE INDEX idx_user_levels_total_points ON user_levels(total_points DESC);

-- 7. 创建更新时间触发器（复用已有函数）
-- 为积分规则表创建触发器
DROP TRIGGER IF EXISTS update_point_rules_updated_at ON point_rules;
CREATE TRIGGER update_point_rules_updated_at
BEFORE UPDATE ON point_rules
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 为成就表创建触发器
DROP TRIGGER IF EXISTS update_achievements_updated_at ON achievements;
CREATE TRIGGER update_achievements_updated_at
BEFORE UPDATE ON achievements
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 为用户成就表创建触发器
DROP TRIGGER IF EXISTS update_user_achievements_updated_at ON user_achievements;
CREATE TRIGGER update_user_achievements_updated_at
BEFORE UPDATE ON user_achievements
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 为用户等级表创建触发器
DROP TRIGGER IF EXISTS update_user_levels_updated_at ON user_levels;
CREATE TRIGGER update_user_levels_updated_at
BEFORE UPDATE ON user_levels
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 8. 添加注释
COMMENT ON TABLE point_rules IS '积分规则表';
COMMENT ON TABLE point_records IS '积分记录表';
COMMENT ON TABLE achievements IS '成就表';
COMMENT ON TABLE user_achievements IS '用户成就表';
COMMENT ON TABLE user_levels IS '用户等级表';

COMMENT ON COLUMN point_rules.action_type IS '行为类型（唯一标识）';
COMMENT ON COLUMN point_rules.daily_limit IS '每日限制次数（0表示不限制）';
COMMENT ON COLUMN point_records.reference_type IS '关联对象类型（question/answer/checkin等）';
COMMENT ON COLUMN point_records.reference_id IS '关联对象ID';
COMMENT ON COLUMN achievements.condition_type IS '达成条件类型（checkin_count/answer_count等）';
COMMENT ON COLUMN achievements.condition_value IS '达成条件数值';
COMMENT ON COLUMN user_achievements.progress IS '当前进度';
COMMENT ON COLUMN user_levels.current_level_points IS '当前等级已获得积分';
COMMENT ON COLUMN user_levels.next_level_points IS '升级所需积分';

-- 9. 插入默认积分规则
INSERT INTO point_rules (action_type, action_name, points, description, daily_limit, is_active) VALUES
('daily_login', '每日登录', 5, '每天首次登录奖励', 1, TRUE),
('create_question', '发布问题', 10, '发布一个问题', 5, TRUE),
('create_answer', '回答问题', 15, '回答一个问题', 10, TRUE),
('best_answer', '最佳答案', 50, '答案被选为最佳答案', 0, TRUE),
('checkin', '打卡', 20, '完成一次打卡', 3, TRUE),
('create_ugc', '发布内容', 30, '发布UGC内容', 3, TRUE),
('create_comment', '发表评论', 5, '发表一条评论', 20, TRUE),
('receive_like', '获得点赞', 2, '内容被点赞', 0, TRUE),
('follow_user', '关注用户', 3, '关注一个用户', 10,true)

