# 📡 API路由文档

> 本文档记录所有API路由及其功能说明，每次更新路由时必须同步更新此文档

**最后更新时间：** 2025-10-31

---

## 📋 目录

- [基础路由](#基础路由)
- [公开路由](#公开路由)
- [认证路由](#认证路由)
- [管理员路由](#管理员路由)

---

## 基础路由

### 健康检查
| 方法 | 完整路径 | 说明 | 认证 |
|------|----------|------|------|
| GET | `http://localhost:8080/health` | 健康检查接口，返回服务状态 | 否 |

### 静态文件
| 方法 | 完整路径 | 说明 | 认证 |
|------|----------|------|------|
| GET | `http://localhost:8080/uploads/{filename}` | 访问上传的文件（图片、文档等） | 否 |

---

## 公开路由

> 以下路由无需认证，任何人都可以访问

### 用户认证
| 方法 | 完整路径 | 说明 | 请求参数 |
|------|----------|------|----------|
| POST | `http://localhost:8080/api/v1/register` | 用户注册 | username, email, password |
| POST | `http://localhost:8080/api/v1/login` | 用户登录，返回JWT token | username, password |

### 非遗项目（公开）
| 方法 | 完整路径 | 说明 | 查询参数 |
|------|----------|------|----------|
| GET | `http://localhost:8080/api/v1/heritage-items` | 获取非遗项目列表（分页） | page, page_size |
| GET | `http://localhost:8080/api/v1/heritage-items/:id` | 获取非遗项目详情，自动增加浏览次数 | - |

### 地理位置（公开）- PostGIS版本 ⭐
| 方法 | 完整路径 | 说明 | 查询参数 |
|------|----------|------|----------|
| GET | `http://localhost:8080/api/v1/locations` | 获取位置列表（分页） | page, page_size, type |
| GET | `http://localhost:8080/api/v1/locations/:id` | 获取位置详情 | - |
| GET | `http://localhost:8080/api/v1/locations/nearby` | 附近位置查询（PostGIS，精确距离计算） | latitude, longitude, radius, type, limit |
| GET | `http://localhost:8080/api/v1/locations/popular` | 热门地点（按打卡次数排序） | limit |
| POST | `http://localhost:8080/api/v1/locations/heatmap` | 热力图数据（PostGIS边界查询） | min_lat, max_lat, min_lon, max_lon |

### UGC内容（公开）
| 方法 | 完整路径 | 说明 | 查询参数 |
|------|----------|------|----------|
| GET | `http://localhost:8080/api/v1/ugc` | 获取所有UGC内容（分页） | page, page_size |
| GET | `http://localhost:8080/api/v1/ugc/type/:type` | 按类型获取UGC内容（video/image/article） | page, page_size |

### 评论（公开）
| 方法 | 完整路径 | 说明 | 路径参数 |
|------|----------|------|----------|
| GET | `http://localhost:8080/api/v1/comments/:target_type/:target_id` | 获取评论列表（树形结构） | target_type: ugc/heritage_item/comment |

### 用户信息（公开）
| 方法 | 完整路径 | 说明 | 路径参数 |
|------|----------|------|----------|
| GET | `http://localhost:8080/api/v1/users/:user_id/stats` | 获取用户统计信息（粉丝数、关注数等） | user_id |
| GET | `http://localhost:8080/api/v1/users/:user_id/profile` | 获取用户公开资料 | user_id |
| GET | `http://localhost:8080/api/v1/users/:user_id/followers` | 获取用户粉丝列表 | user_id |
| GET | `http://localhost:8080/api/v1/users/:user_id/following` | 获取用户关注列表 | user_id |

---

## 认证路由

> 以下所有路由需要在请求头中携带 `Authorization: Bearer {token}`

### 用户管理
| 方法 | 完整路径 | 说明 | 请求参数 |
|------|----------|------|----------|
| GET | `http://localhost:8080/api/v1/auth/profile` | 获取当前用户信息（带缓存） | - |
| POST | `http://localhost:8080/api/v1/auth/logout` | 用户登出，清除缓存 | - |

### 非遗项目管理
| 方法 | 完整路径 | 说明 | 请求参数 |
|------|----------|------|----------|
| POST | `http://localhost:8080/api/v1/auth/heritage-items` | 创建非遗项目 | name, description, category, location, latitude, longitude, cover_image |
| PUT | `http://localhost:8080/api/v1/auth/heritage-items/:id` | 更新非遗项目，自动清除缓存 | name, description, category, location, latitude, longitude, cover_image |

### 地理位置管理
| 方法 | 完整路径 | 说明 | 请求参数 |
|------|----------|------|----------|
| POST | `http://localhost:8080/api/v1/auth/locations` | 创建地理位置（自动生成PostGIS geog字段） | name, address, latitude, longitude, type, item_id |
| PUT | `http://localhost:8080/api/v1/auth/locations/:id` | 更新地理位置（自动更新geog字段） | name, address, latitude, longitude, type |

### 打卡功能
| 方法 | 完整路径 | 说明 | 请求参数 |
|------|----------|------|----------|
| POST | `http://localhost:8080/api/v1/auth/checkins` | 用户打卡（每天每地点限一次） | location_id, notes, images |
| GET | `http://localhost:8080/api/v1/auth/checkins/my` | 获取我的打卡记录 | page, page_size |
| GET | `http://localhost:8080/api/v1/auth/checkins/stats` | 获取我的打卡统计 | - |
| GET | `http://localhost:8080/api/v1/auth/checkins/location/:location_id` | 获取指定地点的打卡记录 | - |
| GET | `http://localhost:8080/api/v1/auth/checkins/popular` | 获取热门打卡地点 | limit |

### UGC内容管理
| 方法 | 完整路径 | 说明 | 请求参数 |
|------|----------|------|----------|
| POST | `http://localhost:8080/api/v1/auth/ugc` | 创建UGC内容（视频/图片/文章） | title, content, content_type, heritage_item_id, media_url |
| GET | `http://localhost:8080/api/v1/auth/ugc/my` | 获取我的UGC内容 | page, page_size |
| PUT | `http://localhost:8080/api/v1/auth/ugc/:id/review` | 审核UGC内容 | status, review_notes |
| GET | `http://localhost:8080/api/v1/auth/ugc/stats` | 获取UGC统计 | - |

### 评论系统
| 方法 | 完整路径 | 说明 | 请求参数 |
|------|----------|------|----------|
| POST | `http://localhost:8080/api/v1/auth/comments` | 发表评论（支持回复） | target_type, target_id, content, parent_id |
| DELETE | `http://localhost:8080/api/v1/auth/comments/:id` | 删除评论（仅自己的） | - |
| POST | `http://localhost:8080/api/v1/auth/comments/:id/like` | 点赞评论 | - |

### 点赞收藏系统
| 方法 | 完整路径 | 说明 | 请求参数 |
|------|----------|------|----------|
| POST | `http://localhost:8080/api/v1/auth/interaction/like` | 点赞内容（UGC/评论） | target_type, target_id |
| POST | `http://localhost:8080/api/v1/auth/interaction/collect` | 收藏内容 | target_type, target_id |
| GET | `http://localhost:8080/api/v1/auth/interaction/my/collections` | 获取我的收藏列表 | page, page_size |

### 文件上传管理
| 方法 | 完整路径 | 说明 | 请求参数 |
|------|----------|------|----------|
| POST | `http://localhost:8080/api/v1/auth/files/upload` | 上传文件（图片/视频/文档） | file, upload_type, target_id |
| GET | `http://localhost:8080/api/v1/auth/files` | 获取文件列表 | upload_type |
| DELETE | `http://localhost:8080/api/v1/auth/files/:id` | 删除文件（仅自己的） | - |

### 用户关系系统
| 方法 | 完整路径 | 说明 | 请求参数 |
|------|----------|------|----------|
| POST | `http://localhost:8080/api/v1/auth/relationships/follow` | 关注用户 | followed_id |
| DELETE | `http://localhost:8080/api/v1/auth/relationships/unfollow/:user_id` | 取消关注 | - |
| GET | `http://localhost:8080/api/v1/auth/relationships/check/:user_id` | 检查关注状态 | - |

### 非遗人认证系统
| 方法 | 完整路径 | 说明 | 请求参数 |
|------|----------|------|----------|
| POST | `http://localhost:8080/api/v1/auth/heritage-people/apply` | 申请非遗人认证 | real_name, id_card, heritage_category, skill_description, proof_materials |
| GET | `http://localhost:8080/api/v1/auth/heritage-people/my-application` | 获取我的认证申请 | - |
| GET | `http://localhost:8080/api/v1/auth/heritage-people/status` | 获取认证状态 | - |

---

## 管理员路由

> 以下所有路由需要管理员权限（role=admin）

### 非遗人认证审核
| 方法 | 完整路径 | 说明 | 请求参数 |
|------|----------|------|----------|
| GET | `http://localhost:8080/api/v1/auth/admin/heritage-people/applications` | 获取所有认证申请（分页） | page, page_size, status |
| PUT | `http://localhost:8080/api/v1/auth/admin/heritage-people/review/:id` | 审核认证申请（通过/拒绝） | status, review_notes |

### 角色管理（RBAC）
| 方法 | 完整路径 | 说明 | 请求参数 |
|------|----------|------|----------|
| POST | `http://localhost:8080/api/v1/auth/admin/roles` | 创建角色 | name, display_name, description |
| GET | `http://localhost:8080/api/v1/auth/admin/roles` | 获取角色列表（带缓存1小时） | page, page_size |
| GET | `http://localhost:8080/api/v1/auth/admin/roles/:id` | 获取角色详情（带权限列表） | - |
| PUT | `http://localhost:8080/api/v1/auth/admin/roles/:id` | 更新角色（系统角色不可修改） | display_name, description |
| DELETE | `http://localhost:8080/api/v1/auth/admin/roles/:id` | 删除角色（系统角色不可删除） | - |
| POST | `http://localhost:8080/api/v1/auth/admin/roles/:id/permissions` | 为角色分配权限 | permission_ids |

### 权限管理（RBAC）
| 方法 | 完整路径 | 说明 | 请求参数 |
|------|----------|------|----------|
| POST | `http://localhost:8080/api/v1/auth/admin/permissions` | 创建权限 | name, display_name, description, resource, action |
| GET | `http://localhost:8080/api/v1/auth/admin/permissions` | 获取权限列表（带缓存1小时） | page, page_size |
| GET | `http://localhost:8080/api/v1/auth/admin/permissions/grouped` | 获取分组权限列表（按资源分组） | - |
| PUT | `http://localhost:8080/api/v1/auth/admin/permissions/:id` | 更新权限 | display_name, description |
| DELETE | `http://localhost:8080/api/v1/auth/admin/permissions/:id` | 删除权限 | - |

### 地理位置管理（管理员）
| 方法 | 完整路径 | 说明 | 请求参数 |
|------|----------|------|----------|
| DELETE | `http://localhost:8080/api/v1/auth/admin/locations/:id` | 删除地理位置 | - |

### 数据统计系统
| 方法 | 完整路径 | 说明 | 缓存时间 |
|------|----------|------|----------|
| GET | `http://localhost:8080/api/v1/auth/stats/platform` | 获取平台统计数据（用户数、项目数等） | 5分钟 |
| GET | `http://localhost:8080/api/v1/auth/stats/content-ranking` | 获取内容排行榜（按浏览/点赞排序） | 5分钟 |
| GET | `http://localhost:8080/api/v1/auth/stats/user-growth` | 获取用户增长数据（按日期统计） | 5分钟 |
| GET | `http://localhost:8080/api/v1/auth/stats/user-behavior` | 获取用户行为数据（打卡、评论等） | 5分钟 |
| GET | `http://localhost:8080/api/v1/auth/stats/hot-locations` | 获取热门地点数据（按打卡次数） | 5分钟 |

---

## 📊 路由统计

- **公开路由**：20个（新增3个PostGIS路由）
- **认证路由**：34个（新增2个位置管理路由）
- **管理员路由**：17个（新增1个位置删除路由）
- **总计**：71个路由

---

## 🔐 认证说明

### 公开路由
- 无需认证，任何人都可以访问

### 认证路由
- 需要在请求头中携带JWT token
- 格式：`Authorization: Bearer {token}`
- token通过登录接口获取

### 管理员路由
- 需要JWT token
- 需要管理员权限（role=admin）
- 普通用户访问会返回403 Forbidden

---

## 📝 更新日志

### 2025-10-31
- ✅ 新增PostGIS地理搜索路由（5个）
- ✅ 更新地理位置管理路由（2个）
- ✅ 新增管理员位置删除路由（1个）
- ✅ 更新路由统计（71个路由）
- ✅ 完善PostGIS功能说明

### 2025-10-30
- 初始版本
- 包含所有现有路由
- 完成服务层重构后的路由整理

---

## 🌟 PostGIS地理搜索功能说明

### 核心特性
1. **精确距离计算** - 使用PostGIS的ST_Distance函数，返回精确的米级距离
2. **高效空间查询** - 使用ST_DWithin和GIST空间索引，查询性能优异
3. **自动字段同步** - 数据库触发器自动同步latitude/longitude到geog字段
4. **多级缓存** - Redis缓存策略，首次查询50-100ms，缓存命中5-10ms
5. **热力图支持** - 支持边界查询，可生成热力图数据

### 查询参数说明

#### 附近位置查询 (GET /api/v1/locations/nearby)
- `latitude` (必填) - 纬度，范围：-90 到 90
- `longitude` (必填) - 经度，范围：-180 到 180
- `radius` (可选) - 半径（米），默认5000，最大100000
- `type` (可选) - 位置类型筛选（如：博物馆、景点）
- `limit` (可选) - 返回数量，默认20，最大100

**示例：**
```
GET /api/v1/locations/nearby?latitude=39.9042&longitude=116.4074&radius=5000&limit=10
```

**返回格式：**
```json
{
  "code": 200,
  "data": {
    "locations": [
      {
        "id": 1,
        "name": "天安门广场",
        "latitude": 39.9042,
        "longitude": 116.4074,
        "distance": 0,
        "type": "景点",
        "created_at": "2025-10-31T10:00:00Z"
      }
    ],
    "count": 5,
    "radius": 5000
  },
  "message": "查询成功"
}
```

#### 热力图数据 (POST /api/v1/locations/heatmap)
**请求体：**
```json
{
  "min_lat": 39.0,
  "max_lat": 40.0,
  "min_lon": 116.0,
  "max_lon": 117.0
}
```

**返回格式：**
```json
{
  "code": 200,
  "data": [
    {
      "latitude": 39.9042,
      "longitude": 116.4074,
      "weight": 10
    }
  ],
  "message": "查询成功"
}
```

### 性能优化
1. **GIST空间索引** - 对geog字段建立空间索引
2. **Redis缓存** - 列表查询5分钟缓存，详情查询30分钟缓存
3. **查询限制** - 限制返回数量，避免大数据量查询
4. **NULL值过滤** - 自动过滤geog为NULL的记录

---

**注意：每次添加、修改或删除路由时，必须同步更新此文档！**

