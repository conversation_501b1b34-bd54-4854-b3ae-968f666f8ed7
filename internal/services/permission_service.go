package services

import (
	"fmt"
	"intangible_cultural_heritage_backend/internal/cache"
	"intangible_cultural_heritage_backend/internal/database"
	"intangible_cultural_heritage_backend/internal/models"
	"time"
)

type PermissionService struct {
	cacheManager *cache.CacheManager
}

func NewPermissionService() *PermissionService {
	return &PermissionService{
		cacheManager: cache.NewCacheManager("permission"),
	}
}

// GetPermissions 获取权限列表（带缓存）
func (s *PermissionService) GetPermissions(page, pageSize int) ([]models.Permission, int64, bool, error) {
	db := database.GetDB()
	
	// 构建缓存键
	cacheKey := fmt.Sprintf("list:page:%d", page)
	
	// 尝试从缓存获取
	var cachedPermissions []models.Permission
	err := s.cacheManager.GetObject(cacheKey, &cachedPermissions)
	if err == nil && len(cachedPermissions) > 0 {
		var total int64
		db.Model(&models.Permission{}).Count(&total)
		return cachedPermissions, total, true, nil
	}
	
	// 缓存未命中，从数据库查询
	var permissions []models.Permission
	var total int64
	
	db.Model(&models.Permission{}).Count(&total)
	
	offset := (page - 1) * pageSize
	if err := db.Offset(offset).Limit(pageSize).Find(&permissions).Error; err != nil {
		return nil, 0, false, ErrDatabaseError
	}
	
	// 缓存结果（1小时）
	s.cacheManager.SetObject(cacheKey, permissions, 1*time.Hour)
	
	return permissions, total, false, nil
}

// GetPermissionsGrouped 获取分组权限列表（带缓存）
func (s *PermissionService) GetPermissionsGrouped() (map[string][]models.Permission, bool, error) {
	db := database.GetDB()
	
	// 尝试从缓存获取
	cacheKey := "grouped"
	var cachedGrouped map[string][]models.Permission
	err := s.cacheManager.GetObject(cacheKey, &cachedGrouped)
	if err == nil && cachedGrouped != nil {
		return cachedGrouped, true, nil
	}
	
	// 缓存未命中，从数据库查询
	var permissions []models.Permission
	if err := db.Find(&permissions).Error; err != nil {
		return nil, false, ErrDatabaseError
	}
	
	// 按资源分组
	grouped := make(map[string][]models.Permission)
	for _, perm := range permissions {
		grouped[perm.Resource] = append(grouped[perm.Resource], perm)
	}
	
	// 缓存结果（1小时）
	s.cacheManager.SetObject(cacheKey, grouped, 1*time.Hour)
	
	return grouped, false, nil
}

// GetPermissionByID 根据ID获取权限
func (s *PermissionService) GetPermissionByID(id uint) (*models.Permission, error) {
	db := database.GetDB()
	
	var permission models.Permission
	if err := db.First(&permission, id).Error; err != nil {
		return nil, ErrPermissionNotFound
	}
	
	return &permission, nil
}

// CreatePermission 创建权限
func (s *PermissionService) CreatePermission(permission *models.Permission) error {
	db := database.GetDB()
	
	// 设置时间戳
	permission.CreatedAt = time.Now()
	permission.UpdatedAt = time.Now()
	
	if err := db.Create(permission).Error; err != nil {
		return ErrDatabaseError
	}
	
	// 清除缓存
	s.cacheManager.DeletePattern("list:*")
	s.cacheManager.Delete("grouped")
	
	return nil
}

// UpdatePermission 更新权限
func (s *PermissionService) UpdatePermission(id uint, updates map[string]interface{}) error {
	db := database.GetDB()
	
	// 检查权限是否存在
	var permission models.Permission
	if err := db.First(&permission, id).Error; err != nil {
		return ErrPermissionNotFound
	}
	
	// 添加更新时间
	updates["updated_at"] = time.Now()
	
	// 更新数据库
	if err := db.Model(&permission).Updates(updates).Error; err != nil {
		return ErrDatabaseError
	}
	
	// 清除缓存
	s.cacheManager.DeletePattern("list:*")
	s.cacheManager.Delete("grouped")
	
	return nil
}

// DeletePermission 删除权限
func (s *PermissionService) DeletePermission(id uint) error {
	db := database.GetDB()
	
	// 检查权限是否存在
	var permission models.Permission
	if err := db.First(&permission, id).Error; err != nil {
		return ErrPermissionNotFound
	}
	
	// 检查是否有角色使用该权限
	var count int64
	db.Model(&models.RolePermission{}).Where("permission_id = ?", id).Count(&count)
	if count > 0 {
		return ErrRoleInUse // 复用错误类型
	}
	
	// 删除权限
	if err := db.Delete(&permission).Error; err != nil {
		return ErrDatabaseError
	}
	
	// 清除缓存
	s.cacheManager.DeletePattern("list:*")
	s.cacheManager.Delete("grouped")
	
	return nil
}

