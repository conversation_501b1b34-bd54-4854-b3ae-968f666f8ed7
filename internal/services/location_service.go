package services

import (
	"intangible_cultural_heritage_backend/internal/cache"
	"intangible_cultural_heritage_backend/internal/database"
	"intangible_cultural_heritage_backend/internal/models"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// LocationService 地理位置服务
type LocationService struct {
	cache *cache.CacheManager
}

// NewLocationService 创建地理位置服务实例
func NewLocationService() *LocationService {
	return &LocationService{
		cache: cache.NewCacheManager("location"),
	}
}

// NearbyLocationsRequest 附近位置查询请求
type NearbyLocationsRequest struct {
	Latitude  float64 `json:"latitude" binding:"required"`
	Longitude float64 `json:"longitude" binding:"required"`
	Radius    float64 `json:"radius"`    // 半径（米），默认5000米
	Type      string  `json:"type"`      // 位置类型筛选
	Limit     int     `json:"limit"`     // 返回数量限制，默认20
}

// LocationWithDistance 带距离的位置信息
type LocationWithDistance struct {
	ID        uint      `json:"id"`
	Name      string    `json:"name"`
	Address   string    `json:"address"`
	Latitude  float64   `json:"latitude"`
	Longitude float64   `json:"longitude"`
	Type      string    `json:"type"`
	ItemID    *uint     `json:"item_id"`
	Distance  float64   `json:"distance"` // 距离（米）
	CreatedAt time.Time `json:"created_at"`
}

// GetNearbyLocations 获取附近位置（使用PostGIS）
func (s *LocationService) GetNearbyLocations(req *NearbyLocationsRequest) ([]LocationWithDistance, error) {
	db := database.GetDB()

	// 设置默认值
	if req.Radius <= 0 {
		req.Radius = 5000 // 默认5公里
	}
	if req.Limit <= 0 {
		req.Limit = 20
	}
	if req.Limit > 100 {
		req.Limit = 100 // 最多返回100条
	}

	// 验证坐标有效性
	if req.Latitude < -90 || req.Latitude > 90 || req.Longitude < -180 || req.Longitude > 180 {
		return nil, ErrInvalidCoordinates
	}

	// 构建查询 - 使用PostGIS的ST_Distance和ST_DWithin
	query := db.Table("locations").
		Select(`
			id,
			name,
			address,
			latitude,
			longitude,
			type,
			item_id,
			created_at,
			ROUND(
				ST_Distance(
					geog,
					ST_SetSRID(ST_MakePoint(?, ?), 4326)::geography
				)::numeric,
				2
			) as distance
		`, req.Longitude, req.Latitude).
		Where("geog IS NOT NULL").
		Where(`
			ST_DWithin(
				geog,
				ST_SetSRID(ST_MakePoint(?, ?), 4326)::geography,
				?
			)
		`, req.Longitude, req.Latitude, req.Radius)

	// 类型筛选
	if req.Type != "" {
		query = query.Where("type = ?", req.Type)
	}

	// 按距离排序并限制数量
	query = query.Order("distance ASC").Limit(req.Limit)

	// 执行查询
	var results []LocationWithDistance
	if err := query.Scan(&results).Error; err != nil {
		return nil, fmt.Errorf("查询附近位置失败: %w", err)
	}

	return results, nil
}

// GetLocationByID 根据ID获取位置详情
func (s *LocationService) GetLocationByID(id uint) (*models.Location, error) {
	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("detail:%d", id)
	var location models.Location

	err := s.cache.GetObject(cacheKey, &location)
	if err == nil {
		return &location, nil
	}

	// 从数据库查询
	db := database.GetDB()
	if err := db.First(&location, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrLocationNotFound
		}
		return nil, fmt.Errorf("查询位置失败: %w", err)
	}

	// 存入缓存（30分钟）
	_ = s.cache.SetObject(cacheKey, location, 30*time.Minute)

	return &location, nil
}

// GetLocationsList 获取位置列表（分页）
func (s *LocationService) GetLocationsList(page, pageSize int, locationType string) ([]models.Location, int64, error) {
	// 参数验证
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10
	}
	if pageSize > 100 {
		pageSize = 100
	}

	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("list:page:%d:size:%d:type:%s", page, pageSize, locationType)
	var cachedData struct {
		Locations []models.Location `json:"locations"`
		Total     int64             `json:"total"`
	}

	err := s.cache.GetObject(cacheKey, &cachedData)
	if err == nil {
		return cachedData.Locations, cachedData.Total, nil
	}

	// 从数据库查询
	db := database.GetDB()
	var locations []models.Location
	var total int64

	query := db.Model(&models.Location{})
	if locationType != "" {
		query = query.Where("type = ?", locationType)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("查询位置总数失败: %w", err)
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).
		Limit(pageSize).
		Order("created_at DESC").
		Find(&locations).Error; err != nil {
		return nil, 0, fmt.Errorf("查询位置列表失败: %w", err)
	}

	// 存入缓存（5分钟）
	cachedData.Locations = locations
	cachedData.Total = total
	_ = s.cache.SetObject(cacheKey, cachedData, 5*time.Minute)

	return locations, total, nil
}

// CreateLocationRequest 创建位置请求
type CreateLocationRequest struct {
	Name      string  `json:"name" binding:"required"`
	Address   string  `json:"address"`
	Latitude  float64 `json:"latitude" binding:"required"`
	Longitude float64 `json:"longitude" binding:"required"`
	Type      string  `json:"type"`
	ItemID    *uint   `json:"item_id"`
}

// CreateLocation 创建位置
func (s *LocationService) CreateLocation(req CreateLocationRequest) (*models.Location, error) {
	// 验证坐标有效性
	if req.Latitude < -90 || req.Latitude > 90 || req.Longitude < -180 || req.Longitude > 180 {
		return nil, ErrInvalidCoordinates
	}

	db := database.GetDB()

	// 创建位置对象
	var itemID uint
	if req.ItemID != nil {
		itemID = *req.ItemID
	}

	location := &models.Location{
		Name:      req.Name,
		Address:   req.Address,
		Latitude:  req.Latitude,
		Longitude: req.Longitude,
		Type:      req.Type,
		ItemID:    itemID,
	}

	// 使用原生SQL插入，触发器会自动设置geog字段
	if err := db.Create(location).Error; err != nil {
		return nil, fmt.Errorf("创建位置失败: %w", err)
	}

	// 清除列表缓存
	_ = s.cache.DeletePattern("list:*")

	return location, nil
}

// UpdateLocationRequest 更新位置请求
type UpdateLocationRequest struct {
	Name      *string  `json:"name"`
	Address   *string  `json:"address"`
	Latitude  *float64 `json:"latitude"`
	Longitude *float64 `json:"longitude"`
	Type      *string  `json:"type"`
	ItemID    *uint    `json:"item_id"`
}

// UpdateLocation 更新位置
func (s *LocationService) UpdateLocation(id uint, req UpdateLocationRequest) error {
	db := database.GetDB()

	// 检查位置是否存在
	var location models.Location
	if err := db.First(&location, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrLocationNotFound
		}
		return fmt.Errorf("查询位置失败: %w", err)
	}

	// 验证坐标有效性
	if req.Latitude != nil {
		if *req.Latitude < -90 || *req.Latitude > 90 {
			return ErrInvalidCoordinates
		}
	}
	if req.Longitude != nil {
		if *req.Longitude < -180 || *req.Longitude > 180 {
			return ErrInvalidCoordinates
		}
	}

	// 构建更新数据
	updates := make(map[string]interface{})
	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Address != nil {
		updates["address"] = *req.Address
	}
	if req.Latitude != nil {
		updates["latitude"] = *req.Latitude
	}
	if req.Longitude != nil {
		updates["longitude"] = *req.Longitude
	}
	if req.Type != nil {
		updates["type"] = *req.Type
	}
	if req.ItemID != nil {
		updates["item_id"] = *req.ItemID
	}

	// 更新字段（触发器会自动更新geog字段）
	if err := db.Model(&location).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新位置失败: %w", err)
	}

	// 清除缓存
	_ = s.cache.Delete(fmt.Sprintf("detail:%d", id))
	_ = s.cache.DeletePattern("list:*")

	return nil
}

// DeleteLocation 删除位置
func (s *LocationService) DeleteLocation(id uint) error {
	db := database.GetDB()

	// 检查位置是否存在
	var location models.Location
	if err := db.First(&location, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrLocationNotFound
		}
		return fmt.Errorf("查询位置失败: %w", err)
	}

	// 删除位置
	if err := db.Delete(&location).Error; err != nil {
		return fmt.Errorf("删除位置失败: %w", err)
	}

	// 清除缓存
	_ = s.cache.Delete(fmt.Sprintf("detail:%d", id))
	_ = s.cache.DeletePattern("list:*")

	return nil
}

// GeoBounds 地理边界
type GeoBounds struct {
	MinLat float64 `json:"min_lat" binding:"required"`
	MaxLat float64 `json:"max_lat" binding:"required"`
	MinLon float64 `json:"min_lon" binding:"required"`
	MaxLon float64 `json:"max_lon" binding:"required"`
}

// HeatmapPoint 热力图点
type HeatmapPoint struct {
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
	Weight    int     `json:"weight"`
}

// GetHeatmapData 获取热力图数据
func (s *LocationService) GetHeatmapData(bounds *GeoBounds) ([]HeatmapPoint, error) {
	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("heatmap:%v", bounds)
	var heatmapData []HeatmapPoint

	err := s.cache.GetObject(cacheKey, &heatmapData)
	if err == nil {
		return heatmapData, nil
	}

	db := database.GetDB()
	query := db.Table("locations").
		Select("latitude, longitude, COUNT(*) as weight").
		Group("latitude, longitude")

	// 如果提供了边界，进行筛选
	if bounds != nil {
		query = query.Where("geog IS NOT NULL").
			Where(`
				ST_Within(
					geog::geometry,
					ST_MakeEnvelope(?, ?, ?, ?, 4326)
				)
			`, bounds.MinLon, bounds.MinLat, bounds.MaxLon, bounds.MaxLat)
	}

	if err := query.Scan(&heatmapData).Error; err != nil {
		return nil, fmt.Errorf("查询热力图数据失败: %w", err)
	}

	// 存入缓存（10分钟）
	_ = s.cache.SetObject(cacheKey, heatmapData, 10*time.Minute)

	return heatmapData, nil
}

// LocationWithStats 带统计信息的位置
type LocationWithStats struct {
	ID           uint      `json:"id"`
	Name         string    `json:"name"`
	Address      string    `json:"address"`
	Latitude     float64   `json:"latitude"`
	Longitude    float64   `json:"longitude"`
	Type         string    `json:"type"`
	ItemID       *uint     `json:"item_id"`
	CheckinCount int       `json:"checkin_count"`
	CreatedAt    time.Time `json:"created_at"`
}

// GetPopularLocations 获取热门地点（基于打卡次数）
func (s *LocationService) GetPopularLocations(limit int) ([]LocationWithStats, error) {
	// 参数验证
	if limit < 1 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}

	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("popular:limit:%d", limit)
	var popularLocations []LocationWithStats

	err := s.cache.GetObject(cacheKey, &popularLocations)
	if err == nil {
		return popularLocations, nil
	}

	db := database.GetDB()
	if err := db.Table("locations").
		Select(`
			locations.id,
			locations.name,
			locations.address,
			locations.latitude,
			locations.longitude,
			locations.type,
			locations.item_id,
			locations.created_at,
			COUNT(checkin_records.id) as checkin_count
		`).
		Joins("LEFT JOIN checkin_records ON checkin_records.location_id = locations.id").
		Group("locations.id").
		Order("checkin_count DESC").
		Limit(limit).
		Scan(&popularLocations).Error; err != nil {
		return nil, fmt.Errorf("查询热门地点失败: %w", err)
	}

	// 存入缓存（10分钟）
	_ = s.cache.SetObject(cacheKey, popularLocations, 10*time.Minute)

	return popularLocations, nil
}

