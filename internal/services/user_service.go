package services

import (
	"encoding/json"
	"fmt"
	"intangible_cultural_heritage_backend/internal/cache"
	"intangible_cultural_heritage_backend/internal/database"
	"intangible_cultural_heritage_backend/internal/models"
	"time"

	"github.com/golang-jwt/jwt/v4"
)

type UserService struct{}

func NewUserService() *UserService {
	return &UserService{}
}

// Register 用户注册
func (s *UserService) Register(req models.UserRegisterRequest) (*models.User, error) {
	db := database.GetDB()
	
	// 检查邮箱是否已存在
	var existingUser models.User
	if err := db.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		return nil, ErrUserAlreadyExists
	}
	
	// 检查用户名是否已存在
	if err := db.Where("username = ?", req.Username).First(&existingUser).Error; err == nil {
		return nil, ErrUsernameExists
	}
	
	// 设置默认角色
	role := "user"
	if req.Role == "non_heritage" || req.Role == "admin" {
		role = req.Role
	}
	
	// 创建新用户
	user := models.User{
		Username:  req.Username,
		Email:     req.Email,
		Role:      role,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	
	// 设置密码
	if err := user.SetPassword(req.Password); err != nil {
		return nil, fmt.Errorf("密码加密失败: %w", err)
	}
	
	// 保存到数据库
	if err := db.Create(&user).Error; err != nil {
		return nil, ErrDatabaseError
	}
	
	return &user, nil
}

// Login 用户登录
func (s *UserService) Login(email, password string) (*models.User, string, error) {
	db := database.GetDB()
	
	// 先尝试从缓存获取用户信息
	cacheKey := fmt.Sprintf("user:email:%s", email)
	cachedUser, err := cache.Get(cacheKey)
	
	var user models.User
	
	if err == nil && cachedUser != "" {
		// 缓存命中
		if err := json.Unmarshal([]byte(cachedUser), &user); err == nil {
			// 验证密码
			if !user.CheckPassword(password) {
				return nil, "", ErrInvalidPassword
			}
			
			// 生成token
			token, err := s.generateToken(&user)
			if err != nil {
				return nil, "", err
			}
			
			return &user, token, nil
		}
	}
	
	// 缓存未命中，从数据库查询
	if err := db.Where("email = ?", email).First(&user).Error; err != nil {
		return nil, "", ErrUserNotFound
	}
	
	// 验证密码
	if !user.CheckPassword(password) {
		return nil, "", ErrInvalidPassword
	}
	
	// 将用户信息缓存到Redis（24小时）
	userJSON, _ := json.Marshal(user)
	cache.Set(cacheKey, string(userJSON), 24*time.Hour)
	
	// 生成token
	token, err := s.generateToken(&user)
	if err != nil {
		return nil, "", err
	}
	
	return &user, token, nil
}

// GetProfile 获取用户信息
func (s *UserService) GetProfile(userID uint) (*models.User, error) {
	db := database.GetDB()
	
	// 先尝试从缓存获取
	cacheKey := fmt.Sprintf("user:profile:%d", userID)
	cachedProfile, err := cache.Get(cacheKey)
	
	if err == nil && cachedProfile != "" {
		var user models.User
		if err := json.Unmarshal([]byte(cachedProfile), &user); err == nil {
			return &user, nil
		}
	}
	
	// 缓存未命中，从数据库查询
	var user models.User
	if err := db.First(&user, userID).Error; err != nil {
		return nil, ErrUserNotFound
	}
	
	// 缓存用户信息（30分钟）
	userJSON, _ := json.Marshal(user)
	cache.Set(cacheKey, string(userJSON), 30*time.Minute)
	
	return &user, nil
}

// generateToken 生成JWT token
func (s *UserService) generateToken(user *models.User) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"user_id": user.ID,
		"email":   user.Email,
		"role":    user.Role,
		"exp":     time.Now().Add(time.Hour * 24).Unix(),
	})
	
	tokenString, err := token.SignedString([]byte("your-secret-key-change-in-production"))
	if err != nil {
		return "", fmt.Errorf("生成token失败: %w", err)
	}
	
	return tokenString, nil
}

// Logout 用户登出（清除缓存）
func (s *UserService) Logout(userID uint) error {
	// 清除用户相关缓存
	cacheKey := fmt.Sprintf("user:profile:%d", userID)
	cache.Delete(cacheKey)
	return nil
}

