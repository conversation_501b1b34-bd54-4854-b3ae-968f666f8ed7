package services

import (
	"fmt"
	"intangible_cultural_heritage_backend/internal/cache"
	"intangible_cultural_heritage_backend/internal/database"
	"intangible_cultural_heritage_backend/internal/models"
	"time"

	"gorm.io/gorm"
)

// AchievementService 成就服务
type AchievementService struct {
	cache *cache.CacheManager
}

// NewAchievementService 创建成就服务实例
func NewAchievementService() *AchievementService {
	return &AchievementService{
		cache: cache.NewCacheManager("achievement"),
	}
}

// GetAchievements 获取成就列表
func (s *AchievementService) GetAchievements(category string) ([]models.Achievement, error) {
	db := database.GetDB()

	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("achievements:%s", category)
	var achievements []models.Achievement
	if err := s.cache.GetObject(cacheKey, &achievements); err == nil {
		return achievements, nil
	}

	// 从数据库查询
	query := db.Where("is_active = ?", true)
	if category != "" {
		query = query.Where("category = ?", category)
	}

	if err := query.Order("category ASC, display_order ASC, condition_value ASC").Find(&achievements).Error; err != nil {
		return nil, fmt.Errorf("查询成就列表失败: %w", err)
	}

	// 缓存结果（1小时）
	s.cache.SetObject(cacheKey, achievements, 1*time.Hour)

	return achievements, nil
}

// GetUserAchievements 获取用户成就列表
func (s *AchievementService) GetUserAchievements(userID uint, isCompleted *bool) ([]models.UserAchievement, error) {
	db := database.GetDB()

	var userAchievements []models.UserAchievement
	query := db.Preload("Achievement").Where("user_id = ?", userID)

	if isCompleted != nil {
		query = query.Where("is_completed = ?", *isCompleted)
	}

	if err := query.Order("is_completed ASC, created_at DESC").Find(&userAchievements).Error; err != nil {
		return nil, fmt.Errorf("查询用户成就失败: %w", err)
	}

	return userAchievements, nil
}

// CheckAndUpdateAchievement 检查并更新成就进度
func (s *AchievementService) CheckAndUpdateAchievement(userID uint, condition string, progress int) error {
	db := database.GetDB()

	// 查询符合条件的成就
	var achievements []models.Achievement
	if err := db.Where("condition_type = ? AND is_active = ?", condition, true).Find(&achievements).Error; err != nil {
		return fmt.Errorf("查询成就失败: %w", err)
	}

	for _, achievement := range achievements {
		// 查询或创建用户成就记录
		var userAchievement models.UserAchievement
		err := db.Where("user_id = ? AND achievement_id = ?", userID, achievement.ID).First(&userAchievement).Error

		if err == gorm.ErrRecordNotFound {
			// 创建新记录
			userAchievement = models.UserAchievement{
				UserID:        userID,
				AchievementID: achievement.ID,
				Progress:      progress,
				IsCompleted:   progress >= achievement.ConditionValue,
			}

			if userAchievement.IsCompleted {
				now := time.Now()
				userAchievement.CompletedAt = &now

				// 发放成就奖励积分
				if achievement.RewardPoints > 0 {
					pointService := NewPointService()
					pointService.AddPoints(userID, "achievement", fmt.Sprintf("完成成就：%s", achievement.Name), "achievement", achievement.ID)
				}
			}

			if err := db.Create(&userAchievement).Error; err != nil {
				return fmt.Errorf("创建用户成就失败: %w", err)
			}
		} else if err != nil {
			return fmt.Errorf("查询用户成就失败: %w", err)
		} else if !userAchievement.IsCompleted {
			// 更新进度
			userAchievement.Progress = progress
			if userAchievement.Progress >= achievement.ConditionValue {
				userAchievement.IsCompleted = true
				now := time.Now()
				userAchievement.CompletedAt = &now

				// 发放成就奖励积分
				if achievement.RewardPoints > 0 {
					pointService := NewPointService()
					pointService.AddPoints(userID, "achievement", fmt.Sprintf("完成成就：%s", achievement.Name), "achievement", achievement.ID)
				}
			}

			if err := db.Save(&userAchievement).Error; err != nil {
				return fmt.Errorf("更新用户成就失败: %w", err)
			}
		}
	}

	return nil
}

// GetUserAchievementStats 获取用户成就统计
func (s *AchievementService) GetUserAchievementStats(userID uint) (map[string]interface{}, error) {
	db := database.GetDB()

	var total int64
	var completed int64

	// 统计总成就数
	db.Model(&models.Achievement{}).Where("is_active = ?", true).Count(&total)

	// 统计已完成成就数
	db.Model(&models.UserAchievement{}).
		Where("user_id = ? AND is_completed = ?", userID, true).
		Count(&completed)

	// 按分类统计
	var categoryStats []struct {
		Category  string
		Total     int64
		Completed int64
	}

	db.Raw(`
		SELECT 
			a.category,
			COUNT(DISTINCT a.id) as total,
			COUNT(DISTINCT CASE WHEN ua.is_completed = true THEN ua.id END) as completed
		FROM achievements a
		LEFT JOIN user_achievements ua ON a.id = ua.achievement_id AND ua.user_id = ?
		WHERE a.is_active = true
		GROUP BY a.category
	`, userID).Scan(&categoryStats)

	return map[string]interface{}{
		"total":          total,
		"completed":      completed,
		"completion_rate": float64(completed) / float64(total) * 100,
		"category_stats": categoryStats,
	}, nil
}

// CreateAchievement 创建成就（管理员）
func (s *AchievementService) CreateAchievement(achievement *models.Achievement) error {
	db := database.GetDB()

	if err := db.Create(achievement).Error; err != nil {
		return fmt.Errorf("创建成就失败: %w", err)
	}

	// 清除缓存
	s.cache.DeletePattern("achievements:*")

	return nil
}

// UpdateAchievement 更新成就（管理员）
func (s *AchievementService) UpdateAchievement(id uint, updates map[string]interface{}) error {
	db := database.GetDB()

	if err := db.Model(&models.Achievement{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新成就失败: %w", err)
	}

	// 清除缓存
	s.cache.DeletePattern("achievements:*")

	return nil
}

// DeleteAchievement 删除成就（管理员）
func (s *AchievementService) DeleteAchievement(id uint) error {
	db := database.GetDB()

	if err := db.Delete(&models.Achievement{}, id).Error; err != nil {
		return fmt.Errorf("删除成就失败: %w", err)
	}

	// 清除缓存
	s.cache.DeletePattern("achievements:*")

	return nil
}

// InitializeUserAchievements 初始化用户成就（新用户注册时调用）
func (s *AchievementService) InitializeUserAchievements(userID uint) error {
	db := database.GetDB()

	// 获取所有活跃成就
	var achievements []models.Achievement
	if err := db.Where("is_active = ?", true).Find(&achievements).Error; err != nil {
		return fmt.Errorf("查询成就列表失败: %w", err)
	}

	// 为用户创建成就记录
	for _, achievement := range achievements {
		userAchievement := models.UserAchievement{
			UserID:        userID,
			AchievementID: achievement.ID,
			Progress:      0,
			IsCompleted:   false,
		}

		// 检查是否已存在
		var count int64
		db.Model(&models.UserAchievement{}).
			Where("user_id = ? AND achievement_id = ?", userID, achievement.ID).
			Count(&count)

		if count == 0 {
			if err := db.Create(&userAchievement).Error; err != nil {
				return fmt.Errorf("创建用户成就失败: %w", err)
			}
		}
	}

	return nil
}

