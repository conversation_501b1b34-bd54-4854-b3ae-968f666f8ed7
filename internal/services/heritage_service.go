package services

import (
	"fmt"
	"intangible_cultural_heritage_backend/internal/cache"
	"intangible_cultural_heritage_backend/internal/database"
	"intangible_cultural_heritage_backend/internal/models"
	"time"
)

type HeritageService struct {
	cacheManager *cache.CacheManager
}

func NewHeritageService() *HeritageService {
	return &HeritageService{
		cacheManager: cache.NewCacheManager("heritage"),
	}
}

// GetAllItems 获取所有非遗项目（带缓存）
func (s *HeritageService) GetAllItems(category string, page, pageSize int) ([]models.HeritageItem, int64, bool, error) {
	db := database.GetDB()
	
	// 构建缓存键
	cacheKey := fmt.Sprintf("list:%s:page:%d", category, page)
	
	// 尝试从缓存获取
	var cachedItems []models.HeritageItem
	err := s.cacheManager.GetObject(cacheKey, &cachedItems)
	if err == nil && len(cachedItems) > 0 {
		// 缓存命中，获取总数
		var total int64
		query := db.Model(&models.HeritageItem{})
		if category != "" && category != "all" {
			query = query.Where("category = ?", category)
		}
		query.Count(&total)
		
		return cachedItems, total, true, nil
	}
	
	// 缓存未命中，从数据库查询
	var items []models.HeritageItem
	var total int64
	
	query := db.Model(&models.HeritageItem{})
	
	// 分类筛选
	if category != "" && category != "all" {
		query = query.Where("category = ?", category)
	}
	
	// 获取总数
	query.Count(&total)
	
	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&items).Error; err != nil {
		return nil, 0, false, ErrDatabaseError
	}
	
	// 缓存结果（5分钟）
	s.cacheManager.SetObject(cacheKey, items, 5*time.Minute)
	
	return items, total, false, nil
}

// GetItemByID 根据ID获取非遗项目（带缓存）
func (s *HeritageService) GetItemByID(id uint) (*models.HeritageItem, bool, error) {
	db := database.GetDB()
	
	// 构建缓存键
	cacheKey := fmt.Sprintf("item:%d", id)
	
	// 尝试从缓存获取
	var cachedItem models.HeritageItem
	err := s.cacheManager.GetObject(cacheKey, &cachedItem)
	if err == nil && cachedItem.ID > 0 {
		// 缓存命中，增加浏览次数（使用Redis计数器）
		viewKey := fmt.Sprintf("view:%d", id)
		s.cacheManager.Incr(viewKey)
		
		return &cachedItem, true, nil
	}
	
	// 缓存未命中，从数据库查询
	var item models.HeritageItem
	if err := db.First(&item, id).Error; err != nil {
		return nil, false, ErrHeritageNotFound
	}
	
	// 缓存结果（30分钟）
	s.cacheManager.SetObject(cacheKey, item, 30*time.Minute)
	
	// 增加浏览次数
	viewKey := fmt.Sprintf("view:%d", id)
	s.cacheManager.Incr(viewKey)
	
	return &item, false, nil
}

// CreateItem 创建非遗项目
func (s *HeritageService) CreateItem(item *models.HeritageItem) error {
	db := database.GetDB()

	// 设置时间戳
	item.CreatedAt = time.Now()

	// 保存到数据库
	if err := db.Create(item).Error; err != nil {
		return ErrDatabaseError
	}

	// 清除列表缓存
	s.cacheManager.DeletePattern("list:*")

	return nil
}

// UpdateItem 更新非遗项目
func (s *HeritageService) UpdateItem(id uint, updates map[string]interface{}) error {
	db := database.GetDB()

	// 检查项目是否存在
	var item models.HeritageItem
	if err := db.First(&item, id).Error; err != nil {
		return ErrHeritageNotFound
	}

	// 更新数据库
	if err := db.Model(&item).Updates(updates).Error; err != nil {
		return ErrDatabaseError
	}

	// 清除相关缓存
	s.cacheManager.Delete(fmt.Sprintf("item:%d", id))
	s.cacheManager.DeletePattern("list:*")

	return nil
}

// GetViewCount 获取浏览次数
func (s *HeritageService) GetViewCount(id uint) (int64, error) {
	viewKey := fmt.Sprintf("view:%d", id)
	count, err := s.cacheManager.Get(viewKey)
	if err != nil {
		return 0, nil
	}
	
	var viewCount int64
	fmt.Sscanf(count, "%d", &viewCount)
	return viewCount, nil
}

