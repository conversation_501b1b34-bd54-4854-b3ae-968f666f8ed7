package services

import (
	"fmt"
	"intangible_cultural_heritage_backend/internal/cache"
	"intangible_cultural_heritage_backend/internal/database"
	"intangible_cultural_heritage_backend/internal/models"
	"time"

	"gorm.io/gorm"
)

// PointService 积分服务
type PointService struct {
	cache *cache.CacheManager
}

// NewPointService 创建积分服务实例
func NewPointService() *PointService {
	return &PointService{
		cache: cache.NewCacheManager("point"),
	}
}

// AddPoints 添加积分
func (s *PointService) AddPoints(userID uint, action, description, targetType string, targetID uint) error {
	db := database.GetDB()

	// 获取积分规则
	var rule models.PointRule
	if err := db.Where("action_type = ? AND is_active = ?", action, true).First(&rule).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("积分规则不存在: %s", action)
		}
		return fmt.Errorf("查询积分规则失败: %w", err)
	}

	// 检查每日限制
	if rule.DailyLimit > 0 {
		today := time.Now().Format("2006-01-02")
		var count int64
		db.Model(&models.PointRecord{}).
			Where("user_id = ? AND action_type = ? AND DATE(created_at) = ?", userID, action, today).
			Count(&count)

		if count >= int64(rule.DailyLimit) {
			return fmt.Errorf("今日该操作已达上限")
		}
	}

	// 开始事务
	return db.Transaction(func(tx *gorm.DB) error {
		// 创建积分记录
		record := &models.PointRecord{
			UserID:        userID,
			ActionType:    action,
			Points:        rule.Points,
			Description:   description,
			ReferenceType: targetType,
			ReferenceID:   targetID,
		}

		if err := tx.Create(record).Error; err != nil {
			return fmt.Errorf("创建积分记录失败: %w", err)
		}

		// 更新用户等级
		var userLevel models.UserLevel
		err := tx.Where("user_id = ?", userID).First(&userLevel).Error
		if err == gorm.ErrRecordNotFound {
			// 创建用户等级记录
			userLevel = models.UserLevel{
				UserID:             userID,
				Level:              1,
				TotalPoints:        rule.Points,
				CurrentLevelPoints: rule.Points,
				NextLevelPoints:    100,
			}
			if err := tx.Create(&userLevel).Error; err != nil {
				return fmt.Errorf("创建用户等级失败: %w", err)
			}
		} else if err != nil {
			return fmt.Errorf("查询用户等级失败: %w", err)
		} else {
			// 更新积分
			userLevel.TotalPoints += rule.Points
			userLevel.CurrentLevelPoints += rule.Points

			// 检查是否升级
			for userLevel.CurrentLevelPoints >= userLevel.NextLevelPoints {
				userLevel.CurrentLevelPoints -= userLevel.NextLevelPoints
				userLevel.Level++
				userLevel.NextLevelPoints = s.calculateNextLevelPoints(userLevel.Level)
			}

			if err := tx.Save(&userLevel).Error; err != nil {
				return fmt.Errorf("更新用户等级失败: %w", err)
			}
		}

		// 清除缓存
		s.cache.Delete(fmt.Sprintf("user_level:%d", userID))
		s.cache.Delete(fmt.Sprintf("user_points:%d", userID))

		return nil
	})
}

// calculateNextLevelPoints 计算下一级所需积分
func (s *PointService) calculateNextLevelPoints(level int) int {
	// 简单的等级公式：100 * level
	return 100 * level
}

// GetUserLevel 获取用户等级信息
func (s *PointService) GetUserLevel(userID uint) (*models.UserLevel, error) {
	db := database.GetDB()

	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("user_level:%d", userID)
	var userLevel models.UserLevel
	if err := s.cache.GetObject(cacheKey, &userLevel); err == nil {
		return &userLevel, nil
	}

	// 从数据库查询
	err := db.Where("user_id = ?", userID).First(&userLevel).Error
	if err == gorm.ErrRecordNotFound {
		// 创建默认等级
		userLevel = models.UserLevel{
			UserID:             userID,
			Level:              1,
			TotalPoints:        0,
			CurrentLevelPoints: 0,
			NextLevelPoints:    100,
		}
		if err := db.Create(&userLevel).Error; err != nil {
			return nil, fmt.Errorf("创建用户等级失败: %w", err)
		}
	} else if err != nil {
		return nil, fmt.Errorf("查询用户等级失败: %w", err)
	}

	// 缓存结果
	s.cache.SetObject(cacheKey, userLevel, 30*time.Minute)

	return &userLevel, nil
}

// GetPointRecords 获取积分记录
func (s *PointService) GetPointRecords(userID uint, page, pageSize int) ([]models.PointRecord, int64, error) {
	db := database.GetDB()

	var records []models.PointRecord
	var total int64

	// 查询总数
	if err := db.Model(&models.PointRecord{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("查询积分记录总数失败: %w", err)
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&records).Error; err != nil {
		return nil, 0, fmt.Errorf("查询积分记录失败: %w", err)
	}

	return records, total, nil
}

// GetPointRanking 获取积分排行榜
func (s *PointService) GetPointRanking(limit int) ([]models.UserLevel, error) {
	db := database.GetDB()

	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("point_ranking:%d", limit)
	var ranking []models.UserLevel
	if err := s.cache.GetObject(cacheKey, &ranking); err == nil {
		return ranking, nil
	}

	// 从数据库查询
	if err := db.Preload("User").
		Order("total_points DESC").
		Limit(limit).
		Find(&ranking).Error; err != nil {
		return nil, fmt.Errorf("查询积分排行榜失败: %w", err)
	}

	// 缓存结果（10分钟）
	s.cache.SetObject(cacheKey, ranking, 10*time.Minute)

	return ranking, nil
}

// GetPointRules 获取积分规则列表
func (s *PointService) GetPointRules() ([]models.PointRule, error) {
	db := database.GetDB()

	// 尝试从缓存获取
	cacheKey := "point_rules:all"
	var rules []models.PointRule
	if err := s.cache.GetObject(cacheKey, &rules); err == nil {
		return rules, nil
	}

	// 从数据库查询
	if err := db.Where("is_active = ?", true).
		Order("action_type ASC").
		Find(&rules).Error; err != nil {
		return nil, fmt.Errorf("查询积分规则失败: %w", err)
	}

	// 缓存结果（1小时）
	s.cache.SetObject(cacheKey, rules, 1*time.Hour)

	return rules, nil
}

// CreatePointRule 创建积分规则（管理员）
func (s *PointService) CreatePointRule(rule *models.PointRule) error {
	db := database.GetDB()

	if err := db.Create(rule).Error; err != nil {
		return fmt.Errorf("创建积分规则失败: %w", err)
	}

	// 清除缓存
	s.cache.Delete("point_rules:all")

	return nil
}

// UpdatePointRule 更新积分规则（管理员）
func (s *PointService) UpdatePointRule(id uint, updates map[string]interface{}) error {
	db := database.GetDB()

	if err := db.Model(&models.PointRule{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新积分规则失败: %w", err)
	}

	// 清除缓存
	s.cache.Delete("point_rules:all")

	return nil
}

// DeletePointRule 删除积分规则（管理员）
func (s *PointService) DeletePointRule(id uint) error {
	db := database.GetDB()

	if err := db.Delete(&models.PointRule{}, id).Error; err != nil {
		return fmt.Errorf("删除积分规则失败: %w", err)
	}

	// 清除缓存
	s.cache.Delete("point_rules:all")

	return nil
}

// GetUserPointStats 获取用户积分统计
func (s *PointService) GetUserPointStats(userID uint) (map[string]interface{}, error) {
	db := database.GetDB()

	// 获取用户等级
	userLevel, err := s.GetUserLevel(userID)
	if err != nil {
		return nil, err
	}

	// 统计各类积分获取情况
	var actionStats []struct {
		ActionType string
		Total      int
		Count      int64
	}

	db.Model(&models.PointRecord{}).
		Select("action_type, SUM(points) as total, COUNT(*) as count").
		Where("user_id = ?", userID).
		Group("action_type").
		Scan(&actionStats)

	return map[string]interface{}{
		"level":              userLevel.Level,
		"total_points":       userLevel.TotalPoints,
		"current_points":     userLevel.CurrentLevelPoints,
		"next_level_points":  userLevel.NextLevelPoints,
		"action_stats":       actionStats,
	}, nil
}

