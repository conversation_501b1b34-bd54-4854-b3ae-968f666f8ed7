package services

import "errors"

// 业务错误定义
var (
	// 用户相关错误
	ErrUserNotFound      = errors.New("用户不存在")
	ErrUserAlreadyExists = errors.New("用户已存在")
	ErrInvalidPassword   = errors.New("密码错误")
	ErrInvalidEmail      = errors.New("邮箱格式错误")
	ErrUsernameExists    = errors.New("用户名已被使用")

	// 非遗项目相关错误
	ErrHeritageNotFound      = errors.New("非遗项目不存在")
	ErrHeritageAlreadyExists = errors.New("非遗项目已存在")
	ErrUnauthorized          = errors.New("无权限操作")

	// 角色权限相关错误
	ErrRoleNotFound            = errors.New("角色不存在")
	ErrRoleAlreadyExists       = errors.New("角色已存在")
	ErrPermissionNotFound      = errors.New("权限不存在")
	ErrPermissionAlreadyExists = errors.New("权限已存在")
	ErrPermissionInUse         = errors.New("权限正在使用中")
	ErrRoleInUse               = errors.New("角色正在使用中")
	ErrSystemRoleProtect       = errors.New("系统角色不可修改")
	
	// 通用错误
	ErrInvalidInput  = errors.New("无效的输入参数")
	ErrDatabaseError = errors.New("数据库操作失败")
	ErrCacheError    = errors.New("缓存操作失败")
	ErrInternalError = errors.New("内部服务错误")

	// 地理位置相关错误
	ErrLocationNotFound   = errors.New("位置不存在")
	ErrInvalidCoordinates = errors.New("无效的地理坐标")

	// 问答相关错误
	ErrQuestionNotFound = errors.New("问题不存在")
	ErrAnswerNotFound   = errors.New("答案不存在")
	ErrPermissionDenied = errors.New("权限不足")
)
