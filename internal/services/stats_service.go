package services

import (
	"fmt"
	"intangible_cultural_heritage_backend/internal/cache"
	"intangible_cultural_heritage_backend/internal/database"
	"intangible_cultural_heritage_backend/internal/models"
	"time"
)

type StatsService struct {
	cacheManager *cache.CacheManager
}

func NewStatsService() *StatsService {
	return &StatsService{
		cacheManager: cache.NewCacheManager("stats"),
	}
}

// GetPlatformStats 获取平台统计数据（带缓存）
func (s *StatsService) GetPlatformStats() (map[string]interface{}, bool, error) {
	db := database.GetDB()
	
	// 尝试从缓存获取
	cacheKey := "platform"
	var cachedStats map[string]interface{}
	err := s.cacheManager.GetObject(cacheKey, &cachedStats)
	if err == nil && cachedStats != nil {
		return cachedStats, true, nil
	}
	
	// 缓存未命中，从数据库查询
	stats := make(map[string]interface{})
	
	// 用户总数
	var userCount int64
	db.Model(&models.User{}).Count(&userCount)
	stats["total_users"] = userCount
	
	// 非遗项目总数
	var heritageCount int64
	db.Model(&models.HeritageItem{}).Count(&heritageCount)
	stats["total_heritage_items"] = heritageCount
	
	// UGC内容总数
	var ugcCount int64
	db.Model(&models.UGCContent{}).Count(&ugcCount)
	stats["total_ugc_content"] = ugcCount
	
	// 打卡总数
	var checkinCount int64
	db.Model(&models.CheckinRecord{}).Count(&checkinCount)
	stats["total_checkins"] = checkinCount
	
	// 评论总数
	var commentCount int64
	db.Model(&models.Comment{}).Count(&commentCount)
	stats["total_comments"] = commentCount
	
	// 今日新增用户
	var todayUsers int64
	today := time.Now().Truncate(24 * time.Hour)
	db.Model(&models.User{}).Where("created_at >= ?", today).Count(&todayUsers)
	stats["today_new_users"] = todayUsers
	
	// 本周新增用户
	weekAgo := time.Now().AddDate(0, 0, -7)
	var weekUsers int64
	db.Model(&models.User{}).Where("created_at >= ?", weekAgo).Count(&weekUsers)
	stats["week_new_users"] = weekUsers
	
	// 缓存结果（5分钟）
	s.cacheManager.SetObject(cacheKey, stats, 5*time.Minute)
	
	return stats, false, nil
}

// GetContentRanking 获取内容排行
func (s *StatsService) GetContentRanking(contentType string, limit int) ([]map[string]interface{}, error) {
	db := database.GetDB()
	
	var results []map[string]interface{}
	
	switch contentType {
	case "heritage":
		// 从Redis获取浏览次数排行
		var items []models.HeritageItem
		db.Limit(limit).Find(&items)
		for _, item := range items {
			viewKey := fmt.Sprintf("view:%d", item.ID)
			viewCount, _ := s.cacheManager.Get(viewKey)
			count := int64(0)
			if viewCount != "" {
				fmt.Sscanf(viewCount, "%d", &count)
			}
			results = append(results, map[string]interface{}{
				"id":         item.ID,
				"name":       item.Name,
				"view_count": count,
			})
		}
	case "ugc":
		// 从点赞表统计
		var contents []models.UGCContent
		db.Limit(limit).Find(&contents)
		for _, content := range contents {
			var likeCount int64
			db.Model(&models.Like{}).Where("target_type = ? AND target_id = ?", "ugc", content.ID).Count(&likeCount)
			results = append(results, map[string]interface{}{
				"id":         content.ID,
				"title":      content.Title,
				"like_count": likeCount,
			})
		}
	}
	
	return results, nil
}

// GetUserGrowth 获取用户增长数据
func (s *StatsService) GetUserGrowth(days int) ([]map[string]interface{}, error) {
	db := database.GetDB()
	
	var results []map[string]interface{}
	
	for i := days - 1; i >= 0; i-- {
		date := time.Now().AddDate(0, 0, -i).Truncate(24 * time.Hour)
		nextDate := date.Add(24 * time.Hour)
		
		var count int64
		db.Model(&models.User{}).
			Where("created_at >= ? AND created_at < ?", date, nextDate).
			Count(&count)
		
		results = append(results, map[string]interface{}{
			"date":  date.Format("2006-01-02"),
			"count": count,
		})
	}
	
	return results, nil
}

// GetUserBehavior 获取用户行为统计
func (s *StatsService) GetUserBehavior() (map[string]interface{}, error) {
	db := database.GetDB()
	
	stats := make(map[string]interface{})
	
	// 活跃用户数（最近7天有操作的用户）
	weekAgo := time.Now().AddDate(0, 0, -7)
	var activeUsers int64
	db.Model(&models.User{}).
		Where("updated_at >= ?", weekAgo).
		Count(&activeUsers)
	stats["active_users"] = activeUsers
	
	// 平均打卡次数
	var totalCheckins int64
	var totalUsers int64
	db.Model(&models.CheckinRecord{}).Count(&totalCheckins)
	db.Model(&models.User{}).Count(&totalUsers)
	if totalUsers > 0 {
		stats["avg_checkins"] = float64(totalCheckins) / float64(totalUsers)
	} else {
		stats["avg_checkins"] = 0
	}
	
	// 平均UGC内容数
	var totalUGC int64
	db.Model(&models.UGCContent{}).Count(&totalUGC)
	if totalUsers > 0 {
		stats["avg_ugc"] = float64(totalUGC) / float64(totalUsers)
	} else {
		stats["avg_ugc"] = 0
	}
	
	return stats, nil
}

// GetHotLocations 获取热门地点
func (s *StatsService) GetHotLocations(limit int) ([]map[string]interface{}, error) {
	db := database.GetDB()
	
	var results []map[string]interface{}
	
	// 按打卡次数排序
	var locations []struct {
		LocationID uint
		Name       string
		Count      int64
	}
	
	db.Table("checkins").
		Select("location_id, COUNT(*) as count").
		Group("location_id").
		Order("count DESC").
		Limit(limit).
		Scan(&locations)
	
	for _, loc := range locations {
		// 获取地点详情
		var location models.Location
		if err := db.First(&location, loc.LocationID).Error; err == nil {
			results = append(results, map[string]interface{}{
				"id":            location.ID,
				"name":          location.Name,
				"checkin_count": loc.Count,
			})
		}
	}
	
	return results, nil
}

