package services

import (
	"fmt"
	"intangible_cultural_heritage_backend/internal/cache"
	"intangible_cultural_heritage_backend/internal/database"
	"intangible_cultural_heritage_backend/internal/models"

	"gorm.io/gorm"
)

// QuestionService 问题服务
type QuestionService struct {
	cache *cache.CacheManager
}

// NewQuestionService 创建问题服务实例
func NewQuestionService() *QuestionService {
	return &QuestionService{
		cache: cache.NewCacheManager("question"),
	}
}

// CreateQuestion 创建问题
func (s *QuestionService) CreateQuestion(question *models.Question) error {
	db := database.GetDB()

	// 设置默认状态
	if question.Status == "" {
		question.Status = "pending"
	}

	if err := db.Create(question).Error; err != nil {
		return fmt.Errorf("创建问题失败: %w", err)
	}

	return nil
}

// GetQuestionByID 根据ID获取问题详情
func (s *QuestionService) GetQuestionByID(id uint, incrementView bool) (*models.Question, error) {
	db := database.GetDB()

	var question models.Question
	if err := db.Preload("User").
		Preload("BestAnswer").
		Preload("BestAnswer.User").
		First(&question, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrQuestionNotFound
		}
		return nil, fmt.Errorf("查询问题失败: %w", err)
	}

	// 增加浏览量
	if incrementView {
		db.Model(&question).UpdateColumn("view_count", gorm.Expr("view_count + ?", 1))
		question.ViewCount++
	}

	return &question, nil
}

// GetQuestionsList 获取问题列表
func (s *QuestionService) GetQuestionsList(page, pageSize int, category, status, keyword string) ([]models.Question, int64, error) {
	db := database.GetDB()

	// 构建查询
	query := db.Model(&models.Question{}).Preload("User")

	// 分类筛选
	if category != "" {
		query = query.Where("category = ?", category)
	}

	// 状态筛选
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 关键词搜索
	if keyword != "" {
		query = query.Where("title LIKE ? OR content LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("查询问题总数失败: %w", err)
	}

	// 分页查询
	var questions []models.Question
	offset := (page - 1) * pageSize
	if err := query.Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&questions).Error; err != nil {
		return nil, 0, fmt.Errorf("查询问题列表失败: %w", err)
	}

	return questions, total, nil
}

// GetMyQuestions 获取我的问题
func (s *QuestionService) GetMyQuestions(userID uint, page, pageSize int) ([]models.Question, int64, error) {
	db := database.GetDB()

	var total int64
	if err := db.Model(&models.Question{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("查询我的问题总数失败: %w", err)
	}

	var questions []models.Question
	offset := (page - 1) * pageSize
	if err := db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&questions).Error; err != nil {
		return nil, 0, fmt.Errorf("查询我的问题失败: %w", err)
	}

	return questions, total, nil
}

// UpdateQuestion 更新问题
func (s *QuestionService) UpdateQuestion(id, userID uint, updates map[string]interface{}) error {
	db := database.GetDB()

	// 检查问题是否存在且属于当前用户
	var question models.Question
	if err := db.First(&question, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return ErrQuestionNotFound
		}
		return fmt.Errorf("查询问题失败: %w", err)
	}

	if question.UserID != userID {
		return ErrPermissionDenied
	}

	// 更新问题
	if err := db.Model(&question).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新问题失败: %w", err)
	}

	return nil
}

// DeleteQuestion 删除问题
func (s *QuestionService) DeleteQuestion(id, userID uint) error {
	db := database.GetDB()

	// 检查问题是否存在且属于当前用户
	var question models.Question
	if err := db.First(&question, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return ErrQuestionNotFound
		}
		return fmt.Errorf("查询问题失败: %w", err)
	}

	if question.UserID != userID {
		return ErrPermissionDenied
	}

	// 软删除
	if err := db.Delete(&question).Error; err != nil {
		return fmt.Errorf("删除问题失败: %w", err)
	}

	return nil
}

// SetBestAnswer 设置最佳答案
func (s *QuestionService) SetBestAnswer(questionID, answerID, userID uint) error {
	db := database.GetDB()

	// 检查问题是否存在且属于当前用户
	var question models.Question
	if err := db.First(&question, questionID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return ErrQuestionNotFound
		}
		return fmt.Errorf("查询问题失败: %w", err)
	}

	if question.UserID != userID {
		return ErrPermissionDenied
	}

	// 检查答案是否存在且属于该问题
	var answer models.Answer
	if err := db.First(&answer, answerID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return ErrAnswerNotFound
		}
		return fmt.Errorf("查询答案失败: %w", err)
	}

	if answer.QuestionID != questionID {
		return fmt.Errorf("答案不属于该问题")
	}

	// 开启事务
	return db.Transaction(func(tx *gorm.DB) error {
		// 取消之前的最佳答案
		if question.BestAnswerID != nil {
			if err := tx.Model(&models.Answer{}).
				Where("id = ?", *question.BestAnswerID).
				Update("is_best", false).Error; err != nil {
				return err
			}
		}

		// 设置新的最佳答案
		if err := tx.Model(&answer).Update("is_best", true).Error; err != nil {
			return err
		}

		// 更新问题状态
		if err := tx.Model(&question).Updates(map[string]interface{}{
			"best_answer_id": answerID,
			"status":         "solved",
		}).Error; err != nil {
			return err
		}

		return nil
	})
}

// FollowQuestion 关注问题
func (s *QuestionService) FollowQuestion(questionID, userID uint) error {
	db := database.GetDB()

	// 检查问题是否存在
	var question models.Question
	if err := db.First(&question, questionID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return ErrQuestionNotFound
		}
		return fmt.Errorf("查询问题失败: %w", err)
	}

	// 检查是否已关注
	var count int64
	db.Model(&models.QuestionFollow{}).
		Where("question_id = ? AND user_id = ?", questionID, userID).
		Count(&count)

	if count > 0 {
		return fmt.Errorf("已关注该问题")
	}

	// 创建关注记录
	follow := &models.QuestionFollow{
		QuestionID: questionID,
		UserID:     userID,
	}

	if err := db.Create(follow).Error; err != nil {
		return fmt.Errorf("关注问题失败: %w", err)
	}

	return nil
}

// UnfollowQuestion 取消关注问题
func (s *QuestionService) UnfollowQuestion(questionID, userID uint) error {
	db := database.GetDB()

	result := db.Where("question_id = ? AND user_id = ?", questionID, userID).
		Delete(&models.QuestionFollow{})

	if result.Error != nil {
		return fmt.Errorf("取消关注失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("未关注该问题")
	}

	return nil
}

// GetFollowedQuestions 获取关注的问题列表
func (s *QuestionService) GetFollowedQuestions(userID uint, page, pageSize int) ([]models.Question, int64, error) {
	db := database.GetDB()

	// 获取总数
	var total int64
	if err := db.Model(&models.QuestionFollow{}).
		Where("user_id = ?", userID).
		Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("查询关注问题总数失败: %w", err)
	}

	// 分页查询
	var follows []models.QuestionFollow
	offset := (page - 1) * pageSize
	if err := db.Where("user_id = ?", userID).
		Preload("Question").
		Preload("Question.User").
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&follows).Error; err != nil {
		return nil, 0, fmt.Errorf("查询关注问题失败: %w", err)
	}

	// 提取问题列表
	questions := make([]models.Question, len(follows))
	for i, follow := range follows {
		questions[i] = follow.Question
	}

	return questions, total, nil
}

// CheckFollowStatus 检查关注状态
func (s *QuestionService) CheckFollowStatus(questionID, userID uint) (bool, error) {
	db := database.GetDB()

	var count int64
	if err := db.Model(&models.QuestionFollow{}).
		Where("question_id = ? AND user_id = ?", questionID, userID).
		Count(&count).Error; err != nil {
		return false, fmt.Errorf("查询关注状态失败: %w", err)
	}

	return count > 0, nil
}

