package services

import (
	"fmt"
	"intangible_cultural_heritage_backend/internal/cache"
	"intangible_cultural_heritage_backend/internal/database"
	"intangible_cultural_heritage_backend/internal/models"

	"gorm.io/gorm"
)

// AnswerService 答案服务
type AnswerService struct {
	cache *cache.CacheManager
}

// NewAnswerService 创建答案服务实例
func NewAnswerService() *AnswerService {
	return &AnswerService{
		cache: cache.NewCacheManager("answer"),
	}
}

// CreateAnswer 创建答案
func (s *AnswerService) CreateAnswer(answer *models.Answer) error {
	db := database.GetDB()

	// 检查问题是否存在
	var question models.Question
	if err := db.First(&question, answer.QuestionID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return ErrQuestionNotFound
		}
		return fmt.Errorf("查询问题失败: %w", err)
	}

	// 创建答案
	if err := db.<PERSON>reate(answer).Error; err != nil {
		return fmt.Errorf("创建答案失败: %w", err)
	}

	return nil
}

// GetAnswerByID 根据ID获取答案详情
func (s *AnswerService) GetAnswerByID(id uint) (*models.Answer, error) {
	db := database.GetDB()

	var answer models.Answer
	if err := db.Preload("User").
		Preload("Question").
		First(&answer, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrAnswerNotFound
		}
		return nil, fmt.Errorf("查询答案失败: %w", err)
	}

	return &answer, nil
}

// GetAnswersByQuestionID 获取问题的所有答案
func (s *AnswerService) GetAnswersByQuestionID(questionID uint, page, pageSize int) ([]models.Answer, int64, error) {
	db := database.GetDB()

	// 检查问题是否存在
	var question models.Question
	if err := db.First(&question, questionID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, 0, ErrQuestionNotFound
		}
		return nil, 0, fmt.Errorf("查询问题失败: %w", err)
	}

	// 获取总数
	var total int64
	if err := db.Model(&models.Answer{}).
		Where("question_id = ?", questionID).
		Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("查询答案总数失败: %w", err)
	}

	// 分页查询（最佳答案排在最前面）
	var answers []models.Answer
	offset := (page - 1) * pageSize
	if err := db.Where("question_id = ?", questionID).
		Preload("User").
		Order("is_best DESC, created_at ASC").
		Offset(offset).
		Limit(pageSize).
		Find(&answers).Error; err != nil {
		return nil, 0, fmt.Errorf("查询答案列表失败: %w", err)
	}

	return answers, total, nil
}

// GetMyAnswers 获取我的答案
func (s *AnswerService) GetMyAnswers(userID uint, page, pageSize int) ([]models.Answer, int64, error) {
	db := database.GetDB()

	// 获取总数
	var total int64
	if err := db.Model(&models.Answer{}).
		Where("user_id = ?", userID).
		Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("查询我的答案总数失败: %w", err)
	}

	// 分页查询
	var answers []models.Answer
	offset := (page - 1) * pageSize
	if err := db.Where("user_id = ?", userID).
		Preload("Question").
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&answers).Error; err != nil {
		return nil, 0, fmt.Errorf("查询我的答案失败: %w", err)
	}

	return answers, total, nil
}

// UpdateAnswer 更新答案
func (s *AnswerService) UpdateAnswer(id, userID uint, updates map[string]interface{}) error {
	db := database.GetDB()

	// 检查答案是否存在且属于当前用户
	var answer models.Answer
	if err := db.First(&answer, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return ErrAnswerNotFound
		}
		return fmt.Errorf("查询答案失败: %w", err)
	}

	if answer.UserID != userID {
		return ErrPermissionDenied
	}

	// 不允许修改is_best字段（只能通过设置最佳答案接口）
	delete(updates, "is_best")

	// 更新答案
	if err := db.Model(&answer).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新答案失败: %w", err)
	}

	return nil
}

// DeleteAnswer 删除答案
func (s *AnswerService) DeleteAnswer(id, userID uint) error {
	db := database.GetDB()

	// 检查答案是否存在且属于当前用户
	var answer models.Answer
	if err := db.First(&answer, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return ErrAnswerNotFound
		}
		return fmt.Errorf("查询答案失败: %w", err)
	}

	if answer.UserID != userID {
		return ErrPermissionDenied
	}

	// 检查是否为最佳答案
	if answer.IsBest {
		return fmt.Errorf("不能删除最佳答案，请先取消最佳答案")
	}

	// 软删除
	if err := db.Delete(&answer).Error; err != nil {
		return fmt.Errorf("删除答案失败: %w", err)
	}

	return nil
}

// GetAnswerStats 获取答案统计
func (s *AnswerService) GetAnswerStats(userID uint) (map[string]interface{}, error) {
	db := database.GetDB()

	var totalAnswers int64
	var bestAnswers int64

	// 总答案数
	if err := db.Model(&models.Answer{}).
		Where("user_id = ?", userID).
		Count(&totalAnswers).Error; err != nil {
		return nil, fmt.Errorf("查询总答案数失败: %w", err)
	}

	// 最佳答案数
	if err := db.Model(&models.Answer{}).
		Where("user_id = ? AND is_best = ?", userID, true).
		Count(&bestAnswers).Error; err != nil {
		return nil, fmt.Errorf("查询最佳答案数失败: %w", err)
	}

	// 总点赞数
	var totalLikes int64
	if err := db.Model(&models.Answer{}).
		Where("user_id = ?", userID).
		Select("COALESCE(SUM(like_count), 0)").
		Scan(&totalLikes).Error; err != nil {
		return nil, fmt.Errorf("查询总点赞数失败: %w", err)
	}

	return map[string]interface{}{
		"total_answers": totalAnswers,
		"best_answers":  bestAnswers,
		"total_likes":   totalLikes,
	}, nil
}

