package services

import (
	"fmt"
	"intangible_cultural_heritage_backend/internal/cache"
	"intangible_cultural_heritage_backend/internal/database"
	"intangible_cultural_heritage_backend/internal/models"
	"time"
)

type RoleService struct {
	cacheManager *cache.CacheManager
}

func NewRoleService() *RoleService {
	return &RoleService{
		cacheManager: cache.NewCacheManager("role"),
	}
}

// GetRoles 获取角色列表（带缓存）
func (s *RoleService) GetRoles(page, pageSize int) ([]models.Role, int64, bool, error) {
	db := database.GetDB()
	
	// 构建缓存键
	cacheKey := fmt.Sprintf("list:page:%d", page)
	
	// 尝试从缓存获取
	var cachedRoles []models.Role
	err := s.cacheManager.GetObject(cacheKey, &cachedRoles)
	if err == nil && len(cachedRoles) > 0 {
		var total int64
		db.Model(&models.Role{}).Count(&total)
		return cachedRoles, total, true, nil
	}
	
	// 缓存未命中，从数据库查询
	var roles []models.Role
	var total int64
	
	db.Model(&models.Role{}).Count(&total)
	
	offset := (page - 1) * pageSize
	if err := db.Offset(offset).Limit(pageSize).Preload("Permissions").Find(&roles).Error; err != nil {
		return nil, 0, false, ErrDatabaseError
	}
	
	// 缓存结果（1小时）
	s.cacheManager.SetObject(cacheKey, roles, 1*time.Hour)
	
	return roles, total, false, nil
}

// GetRoleByID 根据ID获取角色（带缓存）
func (s *RoleService) GetRoleByID(id uint) (*models.Role, bool, error) {
	db := database.GetDB()
	
	// 构建缓存键
	cacheKey := fmt.Sprintf("detail:%d", id)
	
	// 尝试从缓存获取
	var cachedRole models.Role
	err := s.cacheManager.GetObject(cacheKey, &cachedRole)
	if err == nil && cachedRole.ID > 0 {
		return &cachedRole, true, nil
	}
	
	// 缓存未命中，从数据库查询
	var role models.Role
	if err := db.Preload("Permissions").First(&role, id).Error; err != nil {
		return nil, false, ErrRoleNotFound
	}
	
	// 缓存结果（1小时）
	s.cacheManager.SetObject(cacheKey, role, 1*time.Hour)
	
	return &role, false, nil
}

// CreateRole 创建角色
func (s *RoleService) CreateRole(role *models.Role) error {
	db := database.GetDB()
	
	// 设置时间戳
	role.CreatedAt = time.Now()
	role.UpdatedAt = time.Now()
	
	if err := db.Create(role).Error; err != nil {
		return ErrDatabaseError
	}
	
	// 清除列表缓存
	s.cacheManager.DeletePattern("list:*")
	
	return nil
}

// UpdateRole 更新角色
func (s *RoleService) UpdateRole(id uint, updates map[string]interface{}) error {
	db := database.GetDB()
	
	// 检查是否为系统角色
	var role models.Role
	if err := db.First(&role, id).Error; err != nil {
		return ErrRoleNotFound
	}
	
	if role.IsSystem {
		return ErrSystemRoleProtect
	}
	
	// 添加更新时间
	updates["updated_at"] = time.Now()
	
	// 更新数据库
	if err := db.Model(&role).Updates(updates).Error; err != nil {
		return ErrDatabaseError
	}
	
	// 清除相关缓存
	s.cacheManager.Delete(fmt.Sprintf("detail:%d", id))
	s.cacheManager.DeletePattern("list:*")
	
	return nil
}

// DeleteRole 删除角色
func (s *RoleService) DeleteRole(id uint) error {
	db := database.GetDB()
	
	// 检查是否为系统角色
	var role models.Role
	if err := db.First(&role, id).Error; err != nil {
		return ErrRoleNotFound
	}
	
	if role.IsSystem {
		return ErrSystemRoleProtect
	}
	
	// 检查是否有用户使用该角色
	var count int64
	db.Model(&models.UserRole{}).Where("role_id = ?", id).Count(&count)
	if count > 0 {
		return ErrRoleInUse
	}
	
	// 删除角色
	if err := db.Delete(&role).Error; err != nil {
		return ErrDatabaseError
	}
	
	// 清除相关缓存
	s.cacheManager.Delete(fmt.Sprintf("detail:%d", id))
	s.cacheManager.DeletePattern("list:*")
	
	return nil
}

// AssignPermissions 为角色分配权限
func (s *RoleService) AssignPermissions(roleID uint, permissionIDs []uint) error {
	db := database.GetDB()
	
	var role models.Role
	if err := db.First(&role, roleID).Error; err != nil {
		return ErrRoleNotFound
	}
	
	// 获取权限列表
	var permissions []models.Permission
	if err := db.Find(&permissions, permissionIDs).Error; err != nil {
		return ErrDatabaseError
	}
	
	// 替换权限关联
	if err := db.Model(&role).Association("Permissions").Replace(permissions); err != nil {
		return ErrDatabaseError
	}
	
	// 清除相关缓存
	s.cacheManager.Delete(fmt.Sprintf("detail:%d", roleID))
	s.cacheManager.DeletePattern("list:*")
	
	return nil
}

// AssignRoleToUser 为用户分配角色
func (s *RoleService) AssignRoleToUser(userID, roleID uint) error {
	db := database.GetDB()
	
	// 检查角色是否存在
	var role models.Role
	if err := db.First(&role, roleID).Error; err != nil {
		return ErrRoleNotFound
	}
	
	// 检查用户是否存在
	var user models.User
	if err := db.First(&user, userID).Error; err != nil {
		return ErrUserNotFound
	}
	
	// 检查是否已经分配
	var existingUserRole models.UserRole
	if err := db.Where("user_id = ? AND role_id = ?", userID, roleID).First(&existingUserRole).Error; err == nil {
		// 已经分配，直接返回
		return nil
	}
	
	// 创建用户角色关联
	userRole := models.UserRole{
		UserID:    userID,
		RoleID:    roleID,
		CreatedAt: time.Now(),
	}
	
	if err := db.Create(&userRole).Error; err != nil {
		return ErrDatabaseError
	}
	
	return nil
}

// RemoveRoleFromUser 移除用户角色
func (s *RoleService) RemoveRoleFromUser(userID, roleID uint) error {
	db := database.GetDB()
	
	// 删除用户角色关联
	if err := db.Where("user_id = ? AND role_id = ?", userID, roleID).Delete(&models.UserRole{}).Error; err != nil {
		return ErrDatabaseError
	}
	
	return nil
}

// GetUserRoles 获取用户的所有角色
func (s *RoleService) GetUserRoles(userID uint) ([]models.Role, error) {
	db := database.GetDB()
	
	var roles []models.Role
	if err := db.Joins("JOIN user_roles ON user_roles.role_id = roles.id").
		Where("user_roles.user_id = ?", userID).
		Preload("Permissions").
		Find(&roles).Error; err != nil {
		return nil, ErrDatabaseError
	}
	
	return roles, nil
}

