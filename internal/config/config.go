package config

import (
	"os"
)

type Config struct {
	DBHost     string
	DBPort     string
	DBUser     string
	DBPassword string
	DBName     string

	// Redis配置
	RedisAddr     string
	RedisPassword string
	RedisDB       int

	JWTSecret  string
	ServerPort string

	// 文件上传配置
	UploadMaxSize int64  // 最大文件大小（字节）
	AllowedTypes  string // 允许的文件类型
	UploadPath    string // 本地存储路径

	// 阿里云OSS配置（后续使用）
	OSSEndpoint   string
	OSSAccessKey  string
	OSSSecretKey  string
	OSSBucketName string
}

func Load() *Config {
	return &Config{
		DBHost:     getEnv("DB_HOST", "localhost"),
		DBPort:     getEnv("DB_PORT", "5432"),
		DBUser:     getEnv("DB_USER", "postgres"),
		DBPassword: getEnv("DB_PASSWORD", "ssq198123"),
		DBName:     getEnv("DB_NAME", "postgres"),

		// Redis配置
		RedisAddr:     getEnv("REDIS_ADDR", "localhost:6666"),
		RedisPassword: getEnv("REDIS_PASSWORD", "ssq198123"), // Redis密码
		RedisDB:       0,                                     // 默认使用DB 0

		JWTSecret:  getEnv("JWT_SECRET", "heritage-platform-2024-secret-key-with-32-chars"),
		ServerPort: getEnv("SERVER_PORT", "8080"),

		// 文件上传配置
		UploadMaxSize: 10 * 1024 * 1024, // 10MB
		AllowedTypes:  "image/jpeg,image/png,image/gif",
		UploadPath:    getEnv("UPLOAD_PATH", "./uploads"),

		// 阿里云OSS配置
		OSSEndpoint:   getEnv("OSS_ENDPOINT", ""),
		OSSAccessKey:  getEnv("OSS_ACCESS_KEY", ""),
		OSSSecretKey:  getEnv("OSS_SECRET_KEY", ""),
		OSSBucketName: getEnv("OSS_BUCKET_NAME", ""),
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
