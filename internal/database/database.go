package database

import (
	"fmt"
	"intangible_cultural_heritage_backend/internal/config"
	"log"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var DB *gorm.DB

func Init(cfg *config.Config) error {
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable TimeZone=Asia/Shanghai",
		cfg.DBHost, cfg.DBUser, cfg.DBPassword, cfg.DBName, cfg.DBPort)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	DB = db
	log.Println("Database connection established")

	// 注释掉自动迁移，因为我们已经有现成的表
	// err = db.AutoMigrate(&models.User{})
	// if err != nil {
	// 	return fmt.Errorf("failed to auto migrate tables: %w", err)
	// }

	log.Println("Using existing database tables")
	return nil
}

func GetDB() *gorm.DB {
	return DB
}
