package middleware

import (
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
)

// 管理员权限中间件
func AdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 添加调试信息
		log.Printf("DEBUG: AdminMiddleware - Checking user role")

		userRole, exists := c.Get("user_role")
		if !exists {
			// log.Printf("DEBUG: AdminMiddleware - No user_role found")
			c.JSO<PERSON>(http.StatusUnauthorized, gin.H{
				"error": "未授权访问",
			})
			c.Abort()
			return
		}

		log.Printf("DEBUG: AdminMiddleware - User role: %s", userRole)

		if userRole != "admin" {
			// log.Printf("DEBUG: AdminMiddleware - Access denied for role: %s", userRole)
			c.JSON(http.StatusForbidden, gin.H{
				"error": "需要管理员权限",
			})
			c.Abort()
			return
		}

		log.Printf("DEBUG: AdminMiddleware - Access granted for admin")
		c.Next()
	}
}
