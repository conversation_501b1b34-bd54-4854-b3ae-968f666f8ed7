package middleware

import (
	"intangible_cultural_heritage_backend/internal/cache"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v4"
)

func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			c.J<PERSON>(http.StatusUnauthorized, gin.H{
				"error": "缺少授权token",
			})
			c.Abort()
			return
		}

		// 提取token (格式: "Bearer <token>")
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "token格式错误",
			})
			c.Abort()
			return
		}

		tokenString := parts[1]

		// 检查token是否在黑名单中
		isBlacklisted, err := cache.IsTokenBlacklisted(tokenString)
		if err == nil && isBlacklisted {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"error": "token已失效，请重新登录",
			})
			c.Abort()
			return
		}

		// 解析token
		token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
			return []byte("your-secret-key-change-in-production"), nil
		})

		if err != nil || !token.Valid {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "无效的token",
			})
			c.Abort()
			return
		}

		// 提取用户信息
		if claims, ok := token.Claims.(jwt.MapClaims); ok {
			c.Set("user_id", uint(claims["user_id"].(float64)))
			c.Set("user_email", claims["email"].(string))
			c.Set("user_role", claims["role"].(string))
		}

		c.Next()
	}
}
