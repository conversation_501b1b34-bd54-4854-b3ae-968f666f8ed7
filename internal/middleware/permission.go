package middleware

import (
	"net/http"

	"intangible_cultural_heritage_backend/internal/database"
	"intangible_cultural_heritage_backend/internal/models"

	"github.com/gin-gonic/gin"
)

// RequirePermission 权限检查中间件
// 检查用户是否拥有指定权限
func RequirePermission(permissionName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取当前用户ID
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "未授权",
			})
			c.Abort()
			return
		}

		// 检查用户是否拥有该权限
		hasPermission := CheckUserPermission(userID.(uint), permissionName)
		if !hasPermission {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "权限不足",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireAnyPermission 检查用户是否拥有任意一个权限
func RequireAnyPermission(permissionNames ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "未授权",
			})
			c.Abort()
			return
		}

		// 检查是否拥有任意一个权限
		for _, permName := range permissionNames {
			if CheckUserPermission(userID.(uint), permName) {
				c.Next()
				return
			}
		}

		c.JSON(http.StatusForbidden, gin.H{
			"error": "权限不足",
		})
		c.Abort()
	}
}

// RequireAllPermissions 检查用户是否拥有所有权限
func RequireAllPermissions(permissionNames ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "未授权",
			})
			c.Abort()
			return
		}

		// 检查是否拥有所有权限
		for _, permName := range permissionNames {
			if !CheckUserPermission(userID.(uint), permName) {
				c.JSON(http.StatusForbidden, gin.H{
					"error": "权限不足",
				})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// CheckUserPermission 检查用户是否拥有指定权限
func CheckUserPermission(userID uint, permissionName string) bool {
	// 获取用户的所有角色
	var userRoles []models.UserRole
	if err := database.GetDB().Where("user_id = ?", userID).Find(&userRoles).Error; err != nil {
		return false
	}

	if len(userRoles) == 0 {
		return false
	}

	// 提取角色ID
	var roleIDs []uint
	for _, ur := range userRoles {
		roleIDs = append(roleIDs, ur.RoleID)
	}

	// 查询这些角色是否拥有该权限
	var count int64
	database.GetDB().Table("role_permissions").
		Joins("JOIN permissions ON permissions.id = role_permissions.permission_id").
		Where("role_permissions.role_id IN ? AND permissions.name = ?", roleIDs, permissionName).
		Count(&count)

	return count > 0
}

// GetUserPermissions 获取用户的所有权限
func GetUserPermissions(userID uint) []string {
	// 获取用户的所有角色
	var userRoles []models.UserRole
	if err := database.GetDB().Where("user_id = ?", userID).Find(&userRoles).Error; err != nil {
		return []string{}
	}

	if len(userRoles) == 0 {
		return []string{}
	}

	// 提取角色ID
	var roleIDs []uint
	for _, ur := range userRoles {
		roleIDs = append(roleIDs, ur.RoleID)
	}

	// 查询所有权限
	var permissions []models.Permission
	database.GetDB().Table("permissions").
		Joins("JOIN role_permissions ON role_permissions.permission_id = permissions.id").
		Where("role_permissions.role_id IN ?", roleIDs).
		Distinct().
		Find(&permissions)

	// 提取权限名称
	var permissionNames []string
	for _, perm := range permissions {
		permissionNames = append(permissionNames, perm.Name)
	}

	return permissionNames
}
