package handlers

import (
	"intangible_cultural_heritage_backend/internal/models"
	"intangible_cultural_heritage_backend/internal/services"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// AnswerHandler 答案处理器
type AnswerHandler struct {
	service *services.AnswerService
}

// NewAnswerHandler 创建答案处理器实例
func NewAnswerHandler() *AnswerHandler {
	return &AnswerHandler{
		service: services.NewAnswerService(),
	}
}

// CreateAnswer 创建答案
func (h *AnswerHandler) CreateAnswer(c *gin.Context) {
	var req struct {
		QuestionID uint   `json:"question_id" binding:"required"`
		Content    string `json:"content" binding:"required,min=10"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "参数错误: " + err.Error()})
		return
	}

	// 获取当前用户ID
	userID, _ := c.Get("user_id")

	answer := &models.Answer{
		QuestionID: req.QuestionID,
		UserID:     userID.(uint),
		Content:    req.Content,
	}

	if err := h.service.CreateAnswer(answer); err != nil {
		if err == services.ErrQuestionNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建答案失败"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"data":    answer,
		"message": "创建成功",
	})
}

// GetAnswerByID 获取答案详情
func (h *AnswerHandler) GetAnswerByID(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的答案ID"})
		return
	}

	answer, err := h.service.GetAnswerByID(uint(id))
	if err != nil {
		if err == services.ErrAnswerNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "查询答案失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"data":    answer,
		"message": "查询成功",
	})
}

// GetAnswersByQuestionID 获取问题的所有答案
func (h *AnswerHandler) GetAnswersByQuestionID(c *gin.Context) {
	questionID, err := strconv.ParseUint(c.Param("question_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的问题ID"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	answers, total, err := h.service.GetAnswersByQuestionID(uint(questionID), page, pageSize)
	if err != nil {
		if err == services.ErrQuestionNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "查询答案列表失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": gin.H{
			"answers":   answers,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
		"message": "查询成功",
	})
}

// GetMyAnswers 获取我的答案
func (h *AnswerHandler) GetMyAnswers(c *gin.Context) {
	userID, _ := c.Get("user_id")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	answers, total, err := h.service.GetMyAnswers(userID.(uint), page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "查询我的答案失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": gin.H{
			"answers":   answers,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
		"message": "查询成功",
	})
}

// UpdateAnswer 更新答案
func (h *AnswerHandler) UpdateAnswer(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的答案ID"})
		return
	}

	var req struct {
		Content string `json:"content" binding:"required,min=10"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "参数错误"})
		return
	}

	userID, _ := c.Get("user_id")

	updates := map[string]interface{}{
		"content": req.Content,
	}

	if err := h.service.UpdateAnswer(uint(id), userID.(uint), updates); err != nil {
		if err == services.ErrAnswerNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		if err == services.ErrPermissionDenied {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新答案失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
	})
}

// DeleteAnswer 删除答案
func (h *AnswerHandler) DeleteAnswer(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的答案ID"})
		return
	}

	userID, _ := c.Get("user_id")

	if err := h.service.DeleteAnswer(uint(id), userID.(uint)); err != nil {
		if err == services.ErrAnswerNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		if err == services.ErrPermissionDenied {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// GetAnswerStats 获取答案统计
func (h *AnswerHandler) GetAnswerStats(c *gin.Context) {
	userID, _ := c.Get("user_id")

	stats, err := h.service.GetAnswerStats(userID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "查询答案统计失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"data":    stats,
		"message": "查询成功",
	})
}

