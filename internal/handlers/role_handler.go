package handlers

import (
	"net/http"
	"strconv"

	"intangible_cultural_heritage_backend/internal/models"
	"intangible_cultural_heritage_backend/internal/services"

	"github.com/gin-gonic/gin"
)

type RoleHandler struct {
	roleService *services.RoleService
}

func NewRoleHandler() *RoleHandler {
	return &RoleHandler{
		roleService: services.NewRoleService(),
	}
}

// CreateRole 创建角色
// POST /api/v1/auth/admin/roles
func (h *RoleHandler) CreateRole(c *gin.Context) {
	var req models.CreateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	// 创建角色
	role := &models.Role{
		Name:        req.Name,
		DisplayName: req.DisplayName,
		Description: req.Description,
		IsSystem:    false, // 用户创建的角色不是系统角色
	}

	// 调用Service层
	if err := h.roleService.CreateRole(role); err != nil {
		if err == services.ErrRoleAlreadyExists {
			c.JSON(http.StatusConflict, gin.H{
				"error": "角色名已存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "创建角色失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "角色创建成功",
		"data":    role,
	})
}

// GetRoles 获取角色列表
// GET /api/v1/auth/admin/roles
func (h *RoleHandler) GetRoles(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// 调用Service层
	roles, total, cached, err := h.roleService.GetRoles(page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取角色列表失败: " + err.Error(),
		})
		return
	}

	// 构建响应列表
	var responseList []models.RoleListItem
	for _, role := range roles {
		responseList = append(responseList, models.RoleListItem{
			ID:              role.ID,
			Name:            role.Name,
			DisplayName:     role.DisplayName,
			Description:     role.Description,
			IsSystem:        role.IsSystem,
			PermissionCount: len(role.Permissions),
			CreatedAt:       role.CreatedAt,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"data": responseList,
		"pagination": gin.H{
			"page":       page,
			"page_size":  pageSize,
			"total":      total,
			"total_page": (total + int64(pageSize) - 1) / int64(pageSize),
		},
		"cached": cached,
	})
}

// GetRoleByID 获取角色详情
// GET /api/v1/auth/admin/roles/:id
func (h *RoleHandler) GetRoleByID(c *gin.Context) {
	roleID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的角色ID",
		})
		return
	}

	// 调用Service层
	role, cached, err := h.roleService.GetRoleByID(uint(roleID))
	if err != nil {
		if err == services.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "角色不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取角色失败: " + err.Error(),
		})
		return
	}

	// 构建响应
	var permissionResponses []models.PermissionResponse
	for _, perm := range role.Permissions {
		permissionResponses = append(permissionResponses, models.PermissionResponse{
			ID:          perm.ID,
			Name:        perm.Name,
			DisplayName: perm.DisplayName,
			Description: perm.Description,
			Resource:    perm.Resource,
			Action:      perm.Action,
			CreatedAt:   perm.CreatedAt,
			UpdatedAt:   perm.UpdatedAt,
		})
	}

	response := models.RoleResponse{
		ID:          role.ID,
		Name:        role.Name,
		DisplayName: role.DisplayName,
		Description: role.Description,
		IsSystem:    role.IsSystem,
		Permissions: permissionResponses,
		CreatedAt:   role.CreatedAt,
		UpdatedAt:   role.UpdatedAt,
	}

	c.JSON(http.StatusOK, gin.H{
		"data":   response,
		"cached": cached,
	})
}

// UpdateRole 更新角色
// PUT /api/v1/auth/admin/roles/:id
func (h *RoleHandler) UpdateRole(c *gin.Context) {
	roleID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的角色ID",
		})
		return
	}

	var req models.UpdateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	// 构建更新数据
	updates := map[string]interface{}{
		"display_name": req.DisplayName,
		"description":  req.Description,
	}

	// 调用Service层
	if err := h.roleService.UpdateRole(uint(roleID), updates); err != nil {
		if err == services.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "角色不存在",
			})
			return
		}
		if err == services.ErrSystemRoleProtect {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "系统角色不可修改",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "更新角色失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "角色更新成功",
	})
}

// DeleteRole 删除角色
// DELETE /api/v1/auth/admin/roles/:id
func (h *RoleHandler) DeleteRole(c *gin.Context) {
	roleID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的角色ID",
		})
		return
	}

	// 调用Service层
	if err := h.roleService.DeleteRole(uint(roleID)); err != nil {
		if err == services.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "角色不存在",
			})
			return
		}
		if err == services.ErrSystemRoleProtect {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "系统角色不可删除",
			})
			return
		}
		if err == services.ErrRoleInUse {
			c.JSON(http.StatusConflict, gin.H{
				"error": "角色正在使用中，无法删除",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "删除角色失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "角色删除成功",
	})
}

// AssignPermissions 为角色分配权限
// POST /api/v1/auth/admin/roles/:id/permissions
func (h *RoleHandler) AssignPermissions(c *gin.Context) {
	roleID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的角色ID",
		})
		return
	}

	var req models.AssignPermissionsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	// 调用Service层
	if err := h.roleService.AssignPermissions(uint(roleID), req.PermissionIDs); err != nil {
		if err == services.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "角色不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "分配权限失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "权限分配成功",
	})
}

