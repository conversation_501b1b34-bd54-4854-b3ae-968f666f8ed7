package handlers

import (
	"intangible_cultural_heritage_backend/internal/database"
	"intangible_cultural_heritage_backend/internal/models"
	"net/http"

	"github.com/gin-gonic/gin"
)

type InteractionHandler struct {
}

func NewInteractionHandler() *InteractionHandler {
	return &InteractionHandler{}
}

// LikeContent 点赞内容
func (h *InteractionHandler) LikeContent(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req models.LikeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	// 验证目标是否存在
	if !h.validateTargetExists(req.TargetType, req.TargetID) {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "目标内容不存在",
		})
		return
	}

	// 检查是否已经点赞
	var existingLike models.Like
	result := database.GetDB().Where("user_id = ? AND target_type = ? AND target_id = ?",
		userID, req.TargetType, req.TargetID).First(&existingLike)

	if result.RowsAffected > 0 {
		// 已经点赞，执行取消点赞
		database.GetDB().Delete(&existingLike)
		c.JSON(http.StatusOK, gin.H{
			"message": "取消点赞成功",
			"liked":   false,
		})
	} else {
		// 未点赞，执行点赞
		like := models.Like{
			UserID:     userID.(uint),
			TargetType: req.TargetType,
			TargetID:   req.TargetID,
		}
		database.GetDB().Create(&like)
		c.JSON(http.StatusOK, gin.H{
			"message": "点赞成功",
			"liked":   true,
		})
	}
}

// 收藏内容
func (h *InteractionHandler) CollectContent(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req models.CollectRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	// 验证目标是否存在
	if !h.validateTargetExists(req.TargetType, req.TargetID) {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "目标内容不存在",
		})
		return
	}

	// 检查是否已经收藏
	var existingCollection models.Collection
	result := database.GetDB().Where("user_id = ? AND target_type = ? AND target_id = ?",
		userID, req.TargetType, req.TargetID).First(&existingCollection)

	if result.RowsAffected > 0 {
		// 已经收藏，执行取消收藏
		database.GetDB().Delete(&existingCollection)
		c.JSON(http.StatusOK, gin.H{
			"message":   "取消收藏成功",
			"collected": false,
		})
	} else {
		// 未收藏，执行收藏
		collection := models.Collection{
			UserID:     userID.(uint),
			TargetType: req.TargetType,
			TargetID:   req.TargetID,
		}
		database.GetDB().Create(&collection)
		c.JSON(http.StatusOK, gin.H{
			"message":   "收藏成功",
			"collected": true,
		})
	}
}

// 获取我的收藏
func (h *InteractionHandler) GetMyCollections(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var collections []models.Collection
	result := database.GetDB().Where("user_id = ?", userID).Order("created_at DESC").Find(&collections)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取收藏列表失败: " + result.Error.Error(),
		})
		return
	}

	var responses []models.CollectionResponse
	for _, collection := range collections {
		response := models.CollectionResponse{
			ID:         collection.ID,
			UserID:     collection.UserID,
			TargetType: collection.TargetType,
			TargetID:   collection.TargetID,
			CreatedAt:  collection.CreatedAt,
		}

		// 根据目标类型获取详细信息
		switch collection.TargetType {
		case "ugc":
			var ugc models.UGCContent
			if database.GetDB().First(&ugc, collection.TargetID).Error == nil {
				response.Title = ugc.Title
				// 如果有封面图，可以设置CoverImage
			}
		case "heritage_item":
			var item models.HeritageItem
			if database.GetDB().First(&item, collection.TargetID).Error == nil {
				response.Title = item.Name
				response.CoverImage = item.CoverImage
			}
		}

		responses = append(responses, response)
	}

	c.JSON(http.StatusOK, gin.H{
		"data": responses,
	})
}

// 验证目标内容是否存在
func (h *InteractionHandler) validateTargetExists(targetType string, targetID uint) bool {
	switch targetType {
	case "ugc":
		var ugc models.UGCContent
		result := database.GetDB().First(&ugc, targetID)
		return result.Error == nil
	case "heritage_item":
		var item models.HeritageItem
		result := database.GetDB().First(&item, targetID)
		return result.Error == nil
	case "comment":
		var comment models.Comment
		result := database.GetDB().First(&comment, targetID)
		return result.Error == nil
	default:
		return false
	}
}
