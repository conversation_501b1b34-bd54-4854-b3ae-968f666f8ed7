package handlers

import (
	"intangible_cultural_heritage_backend/internal/models"
	"intangible_cultural_heritage_backend/internal/services"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// QuestionHandler 问题处理器
type QuestionHandler struct {
	service *services.QuestionService
}

// NewQuestionHandler 创建问题处理器实例
func NewQuestionHandler() *QuestionHandler {
	return &QuestionHandler{
		service: services.NewQuestionService(),
	}
}

// CreateQuestion 创建问题
func (h *QuestionHandler) CreateQuestion(c *gin.Context) {
	var req struct {
		Title    string `json:"title" binding:"required,min=5,max=200"`
		Content  string `json:"content" binding:"required,min=10"`
		Category string `json:"category"`
		Tags     string `json:"tags"`
	}

	if err := c.Should<PERSON>ind<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "参数错误: " + err.Error()})
		return
	}

	// 获取当前用户ID
	userID, _ := c.Get("user_id")

	question := &models.Question{
		UserID:   userID.(uint),
		Title:    req.Title,
		Content:  req.Content,
		Category: req.Category,
		Tags:     req.Tags,
	}

	if err := h.service.CreateQuestion(question); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建问题失败"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"data":    question,
		"message": "创建成功",
	})
}

// GetQuestionByID 获取问题详情
func (h *QuestionHandler) GetQuestionByID(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的问题ID"})
		return
	}

	question, err := h.service.GetQuestionByID(uint(id), true)
	if err != nil {
		if err == services.ErrQuestionNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "查询问题失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"data":    question,
		"message": "查询成功",
	})
}

// GetQuestionsList 获取问题列表
func (h *QuestionHandler) GetQuestionsList(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	category := c.Query("category")
	status := c.Query("status")
	keyword := c.Query("keyword")

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	questions, total, err := h.service.GetQuestionsList(page, pageSize, category, status, keyword)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "查询问题列表失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": gin.H{
			"questions": questions,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
		"message": "查询成功",
	})
}

// GetMyQuestions 获取我的问题
func (h *QuestionHandler) GetMyQuestions(c *gin.Context) {
	userID, _ := c.Get("user_id")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	questions, total, err := h.service.GetMyQuestions(userID.(uint), page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "查询我的问题失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": gin.H{
			"questions": questions,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
		"message": "查询成功",
	})
}

// UpdateQuestion 更新问题
func (h *QuestionHandler) UpdateQuestion(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的问题ID"})
		return
	}

	var req struct {
		Title    string `json:"title"`
		Content  string `json:"content"`
		Category string `json:"category"`
		Tags     string `json:"tags"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "参数错误"})
		return
	}

	userID, _ := c.Get("user_id")

	updates := make(map[string]interface{})
	if req.Title != "" {
		updates["title"] = req.Title
	}
	if req.Content != "" {
		updates["content"] = req.Content
	}
	if req.Category != "" {
		updates["category"] = req.Category
	}
	if req.Tags != "" {
		updates["tags"] = req.Tags
	}

	if err := h.service.UpdateQuestion(uint(id), userID.(uint), updates); err != nil {
		if err == services.ErrQuestionNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		if err == services.ErrPermissionDenied {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新问题失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
	})
}

// DeleteQuestion 删除问题
func (h *QuestionHandler) DeleteQuestion(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的问题ID"})
		return
	}

	userID, _ := c.Get("user_id")

	if err := h.service.DeleteQuestion(uint(id), userID.(uint)); err != nil {
		if err == services.ErrQuestionNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		if err == services.ErrPermissionDenied {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除问题失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// SetBestAnswer 设置最佳答案
func (h *QuestionHandler) SetBestAnswer(c *gin.Context) {
	questionID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的问题ID"})
		return
	}

	var req struct {
		AnswerID uint `json:"answer_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "参数错误"})
		return
	}

	userID, _ := c.Get("user_id")

	if err := h.service.SetBestAnswer(uint(questionID), req.AnswerID, userID.(uint)); err != nil {
		if err == services.ErrQuestionNotFound || err == services.ErrAnswerNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		if err == services.ErrPermissionDenied {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "设置最佳答案成功",
	})
}

// FollowQuestion 关注问题
func (h *QuestionHandler) FollowQuestion(c *gin.Context) {
	questionID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的问题ID"})
		return
	}

	userID, _ := c.Get("user_id")

	if err := h.service.FollowQuestion(uint(questionID), userID.(uint)); err != nil {
		if err == services.ErrQuestionNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "关注成功",
	})
}


// UnfollowQuestion 取消关注问题
func (h *QuestionHandler) UnfollowQuestion(c *gin.Context) {
	questionID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的问题ID"})
		return
	}

	userID, _ := c.Get("user_id")

	if err := h.service.UnfollowQuestion(uint(questionID), userID.(uint)); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "取消关注成功",
	})
}

// GetFollowedQuestions 获取关注的问题列表
func (h *QuestionHandler) GetFollowedQuestions(c *gin.Context) {
	userID, _ := c.Get("user_id")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	questions, total, err := h.service.GetFollowedQuestions(userID.(uint), page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "查询关注问题失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": gin.H{
			"questions": questions,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
		"message": "查询成功",
	})
}

// CheckFollowStatus 检查关注状态
func (h *QuestionHandler) CheckFollowStatus(c *gin.Context) {
	questionID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的问题ID"})
		return
	}

	userID, _ := c.Get("user_id")

	isFollowed, err := h.service.CheckFollowStatus(uint(questionID), userID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "查询关注状态失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": gin.H{
			"is_followed": isFollowed,
		},
		"message": "查询成功",
	})
}


