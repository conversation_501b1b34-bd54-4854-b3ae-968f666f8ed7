package handlers

import (
	"intangible_cultural_heritage_backend/internal/models"
	"intangible_cultural_heritage_backend/internal/services"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// PointHandler 积分处理器
type PointHandler struct {
	pointService *services.PointService
}

// NewPointHandler 创建积分处理器实例
func NewPointHandler() *PointHandler {
	return &PointHandler{
		pointService: services.NewPointService(),
	}
}

// GetUserLevel 获取用户等级信息
// @Summary 获取用户等级信息
// @Tags 积分
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/auth/points/level [get]
func (h *PointHandler) GetUserLevel(c *gin.Context) {
	userID := c.GetUint("user_id")

	userLevel, err := h.pointService.GetUserLevel(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSO<PERSON>(http.StatusOK, gin.H{
		"code": 200,
		"data": userLevel,
	})
}

// GetPointRecords 获取积分记录
// @Summary 获取积分记录
// @Tags 积分
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/auth/points/records [get]
func (h *PointHandler) GetPointRecords(c *gin.Context) {
	userID := c.GetUint("user_id")

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	records, total, err := h.pointService.GetPointRecords(userID, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": gin.H{
			"records":   records,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// GetPointRanking 获取积分排行榜
// @Summary 获取积分排行榜
// @Tags 积分
// @Accept json
// @Produce json
// @Param limit query int false "排行榜数量" default(100)
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/points/ranking [get]
func (h *PointHandler) GetPointRanking(c *gin.Context) {
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "100"))

	ranking, err := h.pointService.GetPointRanking(limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": ranking,
	})
}

// GetPointRules 获取积分规则列表
// @Summary 获取积分规则列表
// @Tags 积分
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/points/rules [get]
func (h *PointHandler) GetPointRules(c *gin.Context) {
	rules, err := h.pointService.GetPointRules()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": rules,
	})
}

// GetUserPointStats 获取用户积分统计
// @Summary 获取用户积分统计
// @Tags 积分
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/auth/points/stats [get]
func (h *PointHandler) GetUserPointStats(c *gin.Context) {
	userID := c.GetUint("user_id")

	stats, err := h.pointService.GetUserPointStats(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": stats,
	})
}

// CreatePointRule 创建积分规则（管理员）
// @Summary 创建积分规则
// @Tags 积分管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param rule body models.PointRule true "积分规则信息"
// @Success 201 {object} map[string]interface{}
// @Router /api/v1/admin/points/rules [post]
func (h *PointHandler) CreatePointRule(c *gin.Context) {
	var rule models.PointRule
	if err := c.ShouldBindJSON(&rule); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求参数"})
		return
	}

	if err := h.pointService.CreatePointRule(&rule); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "积分规则创建成功",
		"data":    rule,
	})
}

// UpdatePointRule 更新积分规则（管理员）
// @Summary 更新积分规则
// @Tags 积分管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "规则ID"
// @Param updates body map[string]interface{} true "更新内容"
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/admin/points/rules/:id [put]
func (h *PointHandler) UpdatePointRule(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的规则ID"})
		return
	}

	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求参数"})
		return
	}

	if err := h.pointService.UpdatePointRule(uint(id), updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "积分规则更新成功",
	})
}

// DeletePointRule 删除积分规则（管理员）
// @Summary 删除积分规则
// @Tags 积分管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "规则ID"
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/admin/points/rules/:id [delete]
func (h *PointHandler) DeletePointRule(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的规则ID"})
		return
	}

	if err := h.pointService.DeletePointRule(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "积分规则删除成功",
	})
}

