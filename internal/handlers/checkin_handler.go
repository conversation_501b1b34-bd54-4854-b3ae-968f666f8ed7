package handlers

import (
	"encoding/json"
	"intangible_cultural_heritage_backend/internal/database"
	"intangible_cultural_heritage_backend/internal/models"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	// 删除未使用的 "gorm.io/gorm" 导入
)

type CheckinHandler struct {
}

func NewCheckinHandler() *CheckinHandler {
	return &CheckinHandler{}
}

// 用户打卡
func (h *CheckinHandler) Checkin(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req models.CheckinRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	// 检查地理位置是否存在
	var location models.Location
	result := database.GetDB().First(&location, req.LocationID)
	if result.Error != nil {
		c.J<PERSON>N(http.StatusNotFound, gin.H{
			"error": "地理位置不存在",
		})
		return
	}

	// 检查今天是否已经在此地点打卡过
	var existingCheckin models.CheckinRecord
	today := time.Now().Format("2006-01-02")
	result = database.GetDB().Where("user_id = ? AND location_id = ? AND DATE(checkin_time) = ?",
		userID, req.LocationID, today).First(&existingCheckin)
	if result.RowsAffected > 0 {
		c.JSON(http.StatusConflict, gin.H{
			"error": "今天已经在此地点打卡过了",
		})
		return
	}

	// 转换图片数组为JSON字符串
	imagesJSON, err := json.Marshal(req.Images)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "图片数据格式错误",
		})
		return
	}

	checkinRecord := models.CheckinRecord{
		UserID:     userID.(uint),
		LocationID: req.LocationID,
		Notes:      req.Notes,
		Images:     string(imagesJSON),
	}

	result = database.GetDB().Create(&checkinRecord)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "打卡失败: " + result.Error.Error(),
		})
		return
	}

	// 获取用户信息
	var user models.User
	database.GetDB().First(&user, userID)

	// 解析图片JSON
	var images []string
	json.Unmarshal([]byte(checkinRecord.Images), &images)

	response := models.CheckinResponse{
		ID:           checkinRecord.ID,
		UserID:       checkinRecord.UserID,
		LocationID:   checkinRecord.LocationID,
		CheckinTime:  checkinRecord.CheckinTime,
		Notes:        checkinRecord.Notes,
		Images:       images,
		CreatedAt:    checkinRecord.CreatedAt,
		LocationName: location.Name,
		UserName:     user.Username,
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "打卡成功",
		"data":    response,
	})
}

// 获取用户的打卡记录
func (h *CheckinHandler) GetUserCheckins(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var checkins []models.CheckinRecord
	result := database.GetDB().Where("user_id = ?", userID).Order("checkin_time DESC").Find(&checkins)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取打卡记录失败: " + result.Error.Error(),
		})
		return
	}

	var responses []models.CheckinResponse
	for _, checkin := range checkins {
		// 获取地点信息
		var location models.Location
		database.GetDB().First(&location, checkin.LocationID)

		// 获取用户信息
		var user models.User
		database.GetDB().First(&user, checkin.UserID)

		// 解析图片JSON
		var images []string
		json.Unmarshal([]byte(checkin.Images), &images)

		responses = append(responses, models.CheckinResponse{
			ID:           checkin.ID,
			UserID:       checkin.UserID,
			LocationID:   checkin.LocationID,
			CheckinTime:  checkin.CheckinTime,
			Notes:        checkin.Notes,
			Images:       images,
			CreatedAt:    checkin.CreatedAt,
			LocationName: location.Name,
			UserName:     user.Username,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"data": responses,
	})
}

// 获取用户在特定地点的打卡记录
func (h *CheckinHandler) GetLocationCheckins(c *gin.Context) {
	locationID := c.Param("location_id")

	var checkins []models.CheckinRecord
	result := database.GetDB().Where("location_id = ?", locationID).Order("checkin_time DESC").Find(&checkins)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取打卡记录失败: " + result.Error.Error(),
		})
		return
	}

	var responses []models.CheckinResponse
	for _, checkin := range checkins {
		// 获取地点信息
		var location models.Location
		database.GetDB().First(&location, checkin.LocationID)

		// 获取用户信息
		var user models.User
		database.GetDB().First(&user, checkin.UserID)

		// 解析图片JSON
		var images []string
		json.Unmarshal([]byte(checkin.Images), &images)

		responses = append(responses, models.CheckinResponse{
			ID:           checkin.ID,
			UserID:       checkin.UserID,
			LocationID:   checkin.LocationID,
			CheckinTime:  checkin.CheckinTime,
			Notes:        checkin.Notes,
			Images:       images,
			CreatedAt:    checkin.CreatedAt,
			LocationName: location.Name,
			UserName:     user.Username,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"data": responses,
	})
}

// 获取用户打卡统计
func (h *CheckinHandler) GetUserCheckinStats(c *gin.Context) {
	userID, _ := c.Get("user_id")

	stats := models.UserCheckinStats{}

	// 总打卡次数
	var totalCheckins int64
	database.GetDB().Model(&models.CheckinRecord{}).Where("user_id = ?", userID).Count(&totalCheckins)
	stats.TotalCheckins = int(totalCheckins)

	// 打卡过的不同地点数量
	var locationsCount int64
	database.GetDB().Model(&models.CheckinRecord{}).Where("user_id = ?", userID).Distinct("location_id").Count(&locationsCount)
	stats.LocationsCount = int(locationsCount)

	// 最后打卡时间
	var lastCheckin models.CheckinRecord
	database.GetDB().Where("user_id = ?", userID).Order("checkin_time DESC").First(&lastCheckin)
	stats.LastCheckin = lastCheckin.CheckinTime

	// 连续打卡天数（简化版）
	stats.ContinuousDays = h.calculateContinuousDays(userID.(uint))

	c.JSON(http.StatusOK, gin.H{
		"data": stats,
	})
}

// 计算连续打卡天数（简化实现）
func (h *CheckinHandler) calculateContinuousDays(userID uint) int {
	var checkins []models.CheckinRecord
	database.GetDB().Where("user_id = ?", userID).Order("checkin_time DESC").Limit(7).Find(&checkins)

	if len(checkins) == 0 {
		return 0
	}

	continuousDays := 1

	for i := 0; i < len(checkins)-1; i++ {
		currentDay := checkins[i].CheckinTime
		nextDay := checkins[i+1].CheckinTime

		// 检查是否是连续的一天
		if currentDay.Sub(nextDay).Hours() <= 24 {
			continuousDays++
		} else {
			break
		}
	}

	return continuousDays
}

// 获取热门打卡地点
func (h *CheckinHandler) GetPopularLocations(c *gin.Context) {
	type PopularLocation struct {
		LocationID   uint    `json:"location_id"`
		LocationName string  `json:"location_name"`
		CheckinCount int     `json:"checkin_count"`
		Address      string  `json:"address"`
		Latitude     float64 `json:"latitude"`
		Longitude    float64 `json:"longitude"`
	}

	var popularLocations []PopularLocation

	result := database.GetDB().Raw(`
		SELECT 
			l.id as location_id,
			l.name as location_name,
			l.address,
			l.latitude,
			l.longitude,
			COUNT(c.id) as checkin_count
		FROM locations l
		LEFT JOIN checkin_records c ON l.id = c.location_id
		GROUP BY l.id, l.name, l.address, l.latitude, l.longitude
		ORDER BY checkin_count DESC
		LIMIT 10
	`).Scan(&popularLocations)

	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取热门地点失败: " + result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": popularLocations,
	})
}
