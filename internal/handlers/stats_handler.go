package handlers

import (
	"intangible_cultural_heritage_backend/internal/services"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type StatsHandler struct {
	statsService *services.StatsService
}

func NewStatsHandler() *StatsHandler {
	return &StatsHandler{
		statsService: services.NewStatsService(),
	}
}

// 获取平台总数据统计
func (h *StatsHandler) GetPlatformStats(c *gin.Context) {
	// 调用Service层
	stats, cached, err := h.statsService.GetPlatformStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取统计数据失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":   stats,
		"cached": cached,
	})
}

// 获取内容热度排行榜
func (h *StatsHandler) GetContentRanking(c *gin.Context) {
	limit := c.DefaultQuery("limit", "10")
	contentType := c.Query("type") // ugc, heritage_item

	limitInt, _ := strconv.Atoi(limit)

	// 调用Service层
	rankings, err := h.statsService.GetContentRanking(contentType, limitInt)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取排行榜失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": rankings,
	})
}

// 获取用户增长趋势
func (h *StatsHandler) GetUserGrowth(c *gin.Context) {
	days := c.DefaultQuery("days", "30")
	daysInt, _ := strconv.Atoi(days)

	// 调用Service层
	growth, err := h.statsService.GetUserGrowth(daysInt)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取用户增长数据失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": growth,
	})
}

// 获取用户行为分析（简化版）
func (h *StatsHandler) GetUserBehavior(c *gin.Context) {
	// 调用Service层
	behavior, err := h.statsService.GetUserBehavior()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取用户行为数据失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": behavior,
	})
}

// 获取热门地点
func (h *StatsHandler) GetHotLocations(c *gin.Context) {
	limit := c.DefaultQuery("limit", "10")
	limitInt, _ := strconv.Atoi(limit)

	// 调用Service层
	locations, err := h.statsService.GetHotLocations(limitInt)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取热门地点失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": locations,
	})
}
