package handlers

import (
	"fmt"
	"intangible_cultural_heritage_backend/internal/config"
	"intangible_cultural_heritage_backend/internal/database"
	"intangible_cultural_heritage_backend/internal/models"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type FileHandler struct {
	cfg *config.Config
}

func NewFileHandler(cfg *config.Config) *FileHandler {
	// 确保上传目录存在
	os.MkdirAll(cfg.UploadPath, 0755)
	return &FileHandler{cfg: cfg}
}

// 文件上传
func (h *FileHandler) UploadFile(c *gin.Context) {
	userID, _ := c.Get("user_id")

	// 解析表单数据
	var req models.FileUploadRequest
	if err := c.ShouldBind(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	// 获取上传的文件
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请选择要上传的文件",
		})
		return
	}

	// 验证文件大小
	if file.Size > h.cfg.UploadMaxSize {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": fmt.Sprintf("文件大小不能超过 %dMB", h.cfg.UploadMaxSize/(1024*1024)),
		})
		return
	}

	// 验证文件类型
	fileType := file.Header.Get("Content-Type")
	if !h.isAllowedFileType(fileType) {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "不支持的文件类型",
		})
		return
	}

	// 生成唯一文件名
	fileExt := filepath.Ext(file.Filename)
	newFilename := fmt.Sprintf("%d_%d%s", userID, time.Now().UnixNano(), fileExt)
	filePath := filepath.Join(h.cfg.UploadPath, newFilename)

	// 保存文件
	if err := c.SaveUploadedFile(file, filePath); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "文件保存失败: " + err.Error(),
		})
		return
	}

	// 生成访问URL（本地开发环境）
	fileURL := fmt.Sprintf("/uploads/%s", newFilename)

	// 保存文件记录到数据库
	fileRecord := models.FileRecord{
		UserID:     userID.(uint),
		Filename:   file.Filename,
		FileURL:    fileURL,
		FileSize:   file.Size,
		FileType:   fileType,
		UploadType: req.UploadType,
		TargetID:   req.TargetID,
	}

	result := database.GetDB().Create(&fileRecord)
	if result.Error != nil {
		// 如果数据库保存失败，删除已上传的文件
		os.Remove(filePath)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "文件记录保存失败: " + result.Error.Error(),
		})
		return
	}

	response := models.FileUploadResponse{
		ID:         fileRecord.ID,
		Filename:   fileRecord.Filename,
		FileURL:    fileRecord.FileURL,
		FileSize:   fileRecord.FileSize,
		FileType:   fileRecord.FileType,
		UploadType: fileRecord.UploadType,
		TargetID:   fileRecord.TargetID,
		CreatedAt:  fileRecord.CreatedAt,
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "文件上传成功",
		"data":    response,
	})
}

// 验证文件类型是否允许
func (h *FileHandler) isAllowedFileType(fileType string) bool {
	allowedTypes := strings.Split(h.cfg.AllowedTypes, ",")
	for _, allowedType := range allowedTypes {
		if strings.TrimSpace(allowedType) == fileType {
			return true
		}
	}
	return false
}

// 获取文件列表
func (h *FileHandler) GetFiles(c *gin.Context) {
	userID, _ := c.Get("user_id")
	uploadType := c.Query("upload_type")

	var fileRecords []models.FileRecord
	query := database.GetDB().Where("user_id = ?", userID)

	if uploadType != "" {
		query = query.Where("upload_type = ?", uploadType)
	}

	result := query.Order("created_at DESC").Find(&fileRecords)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取文件列表失败: " + result.Error.Error(),
		})
		return
	}

	var responses []models.FileListResponse
	for _, record := range fileRecords {
		// 获取用户信息
		var user models.User
		database.GetDB().First(&user, record.UserID)

		responses = append(responses, models.FileListResponse{
			ID:         record.ID,
			Filename:   record.Filename,
			FileURL:    record.FileURL,
			FileSize:   record.FileSize,
			FileType:   record.FileType,
			UploadType: record.UploadType,
			TargetID:   record.TargetID,
			CreatedAt:  record.CreatedAt,
			UserName:   user.Username,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"data": responses,
	})
}

// 删除文件
func (h *FileHandler) DeleteFile(c *gin.Context) {
	userID, _ := c.Get("user_id")
	fileID := c.Param("id")

	var fileRecord models.FileRecord
	result := database.GetDB().Where("id = ? AND user_id = ?", fileID, userID).First(&fileRecord)
	if result.Error != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "文件不存在或没有权限删除",
		})
		return
	}

	// 删除物理文件
	filePath := filepath.Join(h.cfg.UploadPath, filepath.Base(fileRecord.FileURL))
	if err := os.Remove(filePath); err != nil && !os.IsNotExist(err) {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "文件删除失败: " + err.Error(),
		})
		return
	}

	// 删除数据库记录
	database.GetDB().Delete(&fileRecord)

	c.JSON(http.StatusOK, gin.H{
		"message": "文件删除成功",
	})
}
