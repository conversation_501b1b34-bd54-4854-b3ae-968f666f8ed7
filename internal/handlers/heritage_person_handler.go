package handlers

import (
	"net/http"
	"strconv"
	"time"

	"intangible_cultural_heritage_backend/internal/database"
	"intangible_cultural_heritage_backend/internal/models"

	"github.com/gin-gonic/gin"
)

type HeritagePersonHandler struct {
}

func NewHeritagePersonHandler() *HeritagePersonHandler {
	return &HeritagePersonHandler{}
}

// Apply 提交非遗人认证申请
// POST /api/v1/auth/heritage-people/apply
func (h *HeritagePersonHandler) Apply(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "未授权",
		})
		return
	}

	var req models.SubmitApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	// 检查用户是否已经提交过申请
	var existingApp models.HeritagePersonApplication
	result := database.GetDB().Where("user_id = ?", userID).First(&existingApp)
	if result.RowsAffected > 0 {
		c.JSON(http.StatusConflict, gin.H{
			"error": "您已经提交过认证申请，请勿重复提交",
		})
		return
	}

	// 创建申请记录
	application := models.HeritagePersonApplication{
		UserID:               userID.(uint),
		RealName:             req.RealName,
		IDCard:               req.IDCard,
		HeritageCategory:     req.HeritageCategory,
		HeritageItemID:       req.HeritageItemID,
		CertificationLevel:   req.CertificationLevel,
		CertificationNumber:  req.CertificationNumber,
		CertificationFiles:   req.CertificationFiles,
		ExpertiseDescription: req.ExpertiseDescription,
		ApplicationStatus:    "pending",
		AppliedAt:            time.Now(),
	}

	if err := database.GetDB().Create(&application).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "提交申请失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "认证申请提交成功，请等待管理员审核",
		"data":    application,
	})
}

// GetMyApplication 查看我的认证申请
// GET /api/v1/auth/heritage-people/my-application
func (h *HeritagePersonHandler) GetMyApplication(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "未授权",
		})
		return
	}

	var application models.HeritagePersonApplication
	result := database.GetDB().Where("user_id = ?", userID).First(&application)
	if result.Error != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "未找到认证申请记录",
		})
		return
	}

	// 构建响应数据
	response := models.ApplicationResponse{
		ID:                   application.ID,
		UserID:               application.UserID,
		RealName:             application.RealName,
		IDCard:               application.IDCard,
		HeritageCategory:     application.HeritageCategory,
		HeritageItemID:       application.HeritageItemID,
		CertificationLevel:   application.CertificationLevel,
		CertificationNumber:  application.CertificationNumber,
		CertificationFiles:   application.CertificationFiles,
		ExpertiseDescription: application.ExpertiseDescription,
		ApplicationStatus:    application.ApplicationStatus,
		AppliedAt:            application.AppliedAt,
		ReviewedAt:           application.ReviewedAt,
		ReviewerID:           application.ReviewerID,
		ReviewComment:        application.ReviewComment,
		CreatedAt:            application.CreatedAt,
		UpdatedAt:            application.UpdatedAt,
	}

	// 如果有关联的非遗项目，获取项目名称
	if application.HeritageItemID != nil {
		var heritageItem models.HeritageItem
		if err := database.GetDB().First(&heritageItem, *application.HeritageItemID).Error; err == nil {
			response.HeritageItemName = heritageItem.Name
		}
	}

	// 如果有审核人，获取审核人姓名
	if application.ReviewerID != nil {
		var reviewer models.User
		if err := database.GetDB().First(&reviewer, *application.ReviewerID).Error; err == nil {
			response.ReviewerName = reviewer.Username
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"data": response,
	})
}

// GetStatus 查看认证状态
// GET /api/v1/auth/heritage-people/status
func (h *HeritagePersonHandler) GetStatus(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "未授权",
		})
		return
	}

	var application models.HeritagePersonApplication
	result := database.GetDB().Where("user_id = ?", userID).First(&application)

	if result.Error != nil {
		// 未提交申请
		c.JSON(http.StatusOK, gin.H{
			"status":    "not_applied",
			"message":   "您还未提交认证申请",
			"can_apply": true,
		})
		return
	}

	// 已提交申请
	statusMessage := map[string]string{
		"pending":  "您的申请正在审核中，请耐心等待",
		"approved": "恭喜！您的申请已通过审核",
		"rejected": "很遗憾，您的申请未通过审核",
	}

	c.JSON(http.StatusOK, gin.H{
		"status":      application.ApplicationStatus,
		"message":     statusMessage[application.ApplicationStatus],
		"can_apply":   false,
		"applied_at":  application.AppliedAt,
		"reviewed_at": application.ReviewedAt,
	})
}

// GetApplications 管理员查看所有认证申请（分页）
// GET /api/v1/auth/admin/heritage-people/applications
func (h *HeritagePersonHandler) GetApplications(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	status := c.Query("status") // pending, approved, rejected

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize

	// 构建查询
	query := database.GetDB().Model(&models.HeritagePersonApplication{})

	// 状态筛选
	if status != "" {
		query = query.Where("application_status = ?", status)
	}

	// 获取总数
	var total int64
	query.Count(&total)

	// 获取申请列表
	var applications []models.HeritagePersonApplication
	if err := query.Order("applied_at DESC").
		Limit(pageSize).
		Offset(offset).
		Find(&applications).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取申请列表失败: " + err.Error(),
		})
		return
	}

	// 构建响应列表
	var responseList []models.ApplicationListItem
	for _, app := range applications {
		// 获取用户信息
		var user models.User
		username := ""
		if err := database.GetDB().First(&user, app.UserID).Error; err == nil {
			username = user.Username
		}

		responseList = append(responseList, models.ApplicationListItem{
			ID:                app.ID,
			UserID:            app.UserID,
			Username:          username,
			RealName:          app.RealName,
			HeritageCategory:  app.HeritageCategory,
			ApplicationStatus: app.ApplicationStatus,
			AppliedAt:         app.AppliedAt,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"data": responseList,
		"pagination": gin.H{
			"page":       page,
			"page_size":  pageSize,
			"total":      total,
			"total_page": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	})
}

// ReviewApplication 管理员审核认证申请
// PUT /api/v1/auth/admin/heritage-people/review/:id
func (h *HeritagePersonHandler) ReviewApplication(c *gin.Context) {
	// 获取申请ID
	applicationID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的申请ID",
		})
		return
	}

	// 获取当前管理员ID
	reviewerID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "未授权",
		})
		return
	}

	var req models.ReviewApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	// 查找申请记录
	var application models.HeritagePersonApplication
	if err := database.GetDB().First(&application, applicationID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "未找到该申请记录",
		})
		return
	}

	// 检查申请状态
	if application.ApplicationStatus != "pending" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "该申请已经被审核过了",
		})
		return
	}

	// 更新申请状态
	now := time.Now()
	reviewerIDUint := reviewerID.(uint)
	application.ApplicationStatus = req.Status
	application.ReviewComment = req.ReviewComment
	application.ReviewedAt = &now
	application.ReviewerID = &reviewerIDUint

	if err := database.GetDB().Save(&application).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "审核失败: " + err.Error(),
		})
		return
	}

	// 如果审核通过，更新用户角色为 non_heritage
	if req.Status == "approved" {
		var user models.User
		if err := database.GetDB().First(&user, application.UserID).Error; err == nil {
			user.Role = "non_heritage"
			database.GetDB().Save(&user)
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "审核成功",
		"data":    application,
	})
}
