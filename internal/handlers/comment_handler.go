package handlers

import (
	"fmt"
	"intangible_cultural_heritage_backend/internal/database"
	"intangible_cultural_heritage_backend/internal/models"
	// "intangible_cultural_heritage_backend/internal/services"  // 暂时注释，等服务层实现后再启用
	"net/http"

	"github.com/gin-gonic/gin"
)

type CommentHandler struct {
	// 暂时注释掉，等服务层实现后再启用
	// notificationService *services.NotificationService
}

func NewCommentHandler() *CommentHandler {
	return &CommentHandler{
		// notificationService: services.NewNotificationService(),
	}
}

// 创建评论
func (h *CommentHandler) CreateComment(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req models.CreateCommentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	// 处理 ParentID：如果为0则设为nil
	var parentID *uint
	if req.ParentID != 0 {
		parentID = &req.ParentID
	}

	comment := models.Comment{
		UserID:     userID.(uint),
		TargetType: req.TargetType,
		TargetID:   req.TargetID,
		Content:    req.Content,
		ParentID:   parentID,
	}

	result := database.GetDB().Create(&comment)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "创建评论失败: " + result.Error.Error(),
		})
		return
	}

	// 发送通知（如果是回复评论）
	if parentID != nil {
		go h.sendCommentNotification(comment)
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "评论成功",
		"data":    comment,
	})
}

// 删除评论
func (h *CommentHandler) DeleteComment(c *gin.Context) {
	userID, _ := c.Get("user_id")
	commentID := c.Param("id")

	var comment models.Comment
	result := database.GetDB().First(&comment, commentID)
	if result.Error != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "评论不存在",
		})
		return
	}

	// 检查权限：只能删除自己的评论
	if comment.UserID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{
			"error": "无权删除此评论",
		})
		return
	}

	// 删除评论
	result = database.GetDB().Delete(&comment)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "删除评论失败: " + result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "删除成功",
	})
}

// 获取评论列表
func (h *CommentHandler) GetComments(c *gin.Context) {
	targetType := c.Param("target_type")
	targetID := c.Param("target_id")

	var comments []models.Comment
	result := database.GetDB().
		Where("target_type = ? AND target_id = ?", targetType, targetID).
		Order("created_at DESC").
		Find(&comments)

	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取评论失败: " + result.Error.Error(),
		})
		return
	}

	// 构建评论树形结构
	commentTree := h.buildCommentTree(comments)

	c.JSON(http.StatusOK, gin.H{
		"data": commentTree,
	})
}

// 点赞评论
func (h *CommentHandler) LikeComment(c *gin.Context) {
	userID, _ := c.Get("user_id")
	commentID := c.Param("id")

	// 检查评论是否存在
	var comment models.Comment
	result := database.GetDB().First(&comment, commentID)
	if result.Error != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "评论不存在",
		})
		return
	}

	// 检查是否已经点赞
	var existingLike models.Like
	result = database.GetDB().Where("user_id = ? AND target_type = ? AND target_id = ?",
		userID, "comment", commentID).First(&existingLike)

	if result.RowsAffected > 0 {
		// 已点赞，取消点赞
		database.GetDB().Delete(&existingLike)
		c.JSON(http.StatusOK, gin.H{
			"message": "取消点赞",
			"liked":   false,
		})
		return
	}

	// 创建点赞记录
	like := models.Like{
		UserID:     userID.(uint),
		TargetType: "comment",
		TargetID:   comment.ID,
	}

	result = database.GetDB().Create(&like)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "点赞失败: " + result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "点赞成功",
		"liked":   true,
	})
}

// 构建评论树形结构
func (h *CommentHandler) buildCommentTree(comments []models.Comment) []models.CommentResponse {
	// 创建评论映射
	commentMap := make(map[uint]*models.CommentResponse)
	var rootComments []models.CommentResponse

	// 第一遍：创建所有评论节点
	for _, comment := range comments {
		// 获取用户信息
		var user models.User
		database.GetDB().First(&user, comment.UserID)

		// 处理 ParentID：如果为nil则设为0
		parentID := uint(0)
		if comment.ParentID != nil {
			parentID = *comment.ParentID
		}

		commentResp := models.CommentResponse{
			ID:         comment.ID,
			UserID:     comment.UserID,
			UserName:   user.Username,
			UserAvatar: user.AvatarURL,
			TargetType: comment.TargetType,
			TargetID:   comment.TargetID,
			Content:    comment.Content,
			ParentID:   parentID,
			LikeCount:  comment.LikeCount,
			Status:     comment.Status,
			CreatedAt:  comment.CreatedAt,
			Replies:    []models.CommentResponse{},
		}

		commentMap[comment.ID] = &commentResp
	}

	// 第二遍：构建树形结构
	for _, comment := range comments {
		commentResp := commentMap[comment.ID]
		if comment.ParentID == nil {
			// 根评论
			rootComments = append(rootComments, *commentResp)
		} else {
			// 子评论
			if parent, exists := commentMap[*comment.ParentID]; exists {
				parent.Replies = append(parent.Replies, *commentResp)
			}
		}
	}

	return rootComments
}

// 发送评论通知（异步）
func (h *CommentHandler) sendCommentNotification(comment models.Comment) {
	// 获取父评论
	if comment.ParentID == nil {
		return
	}

	var parentComment models.Comment
	if err := database.GetDB().First(&parentComment, *comment.ParentID).Error; err != nil {
		return
	}

	// 获取评论作者
	var author models.User
	if err := database.GetDB().First(&author, comment.UserID).Error; err != nil {
		return
	}

	// 获取目标用户（父评论的作者）
	targetUserID := parentComment.UserID
	authorID := comment.UserID

	// 不给自己发通知
	if authorID == targetUserID {
		return
	}

	// 发送评论通知（暂时注释，等服务层实现后再启用）
	// err := h.notificationService.CreateCommentNotification(comment.ID, targetUserID, authorID)
	// if err != nil {
	// 	// 记录错误但不影响主流程
	// 	fmt.Printf("发送评论通知失败: %v\n", err)
	// }
	fmt.Printf("TODO: 发送评论通知给用户 %d，评论者 %d 回复了您的评论\n", targetUserID, authorID)
}
