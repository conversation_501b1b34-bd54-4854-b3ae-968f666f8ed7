package handlers

import (
	"net/http"
	"strconv"

	"intangible_cultural_heritage_backend/internal/models"
	"intangible_cultural_heritage_backend/internal/services"

	"github.com/gin-gonic/gin"
)

type PermissionHandler struct {
	permissionService *services.PermissionService
}

func NewPermissionHandler() *PermissionHandler {
	return &PermissionHandler{
		permissionService: services.NewPermissionService(),
	}
}

// CreatePermission 创建权限
// POST /api/v1/auth/admin/permissions
func (h *PermissionHandler) CreatePermission(c *gin.Context) {
	var req models.CreatePermissionRequest
	if err := c.ShouldBindJ<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	permission := &models.Permission{
		Name:        req.Name,
		DisplayName: req.DisplayName,
		Description: req.Description,
		Resource:    req.Resource,
		Action:      req.Action,
	}

	// 调用Service层
	if err := h.permissionService.CreatePermission(permission); err != nil {
		if err == services.ErrPermissionAlreadyExists {
			c.JSON(http.StatusConflict, gin.H{
				"error": "权限名已存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "创建权限失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "权限创建成功",
		"data":    permission,
	})
}

// GetPermissions 获取权限列表
// GET /api/v1/auth/admin/permissions
func (h *PermissionHandler) GetPermissions(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "50"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 50
	}

	// 调用Service层
	permissions, total, cached, err := h.permissionService.GetPermissions(page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取权限列表失败: " + err.Error(),
		})
		return
	}

	// 构建响应列表
	var responseList []models.PermissionListItem
	for _, perm := range permissions {
		responseList = append(responseList, models.PermissionListItem{
			ID:          perm.ID,
			Name:        perm.Name,
			DisplayName: perm.DisplayName,
			Resource:    perm.Resource,
			Action:      perm.Action,
			CreatedAt:   perm.CreatedAt,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"data": responseList,
		"pagination": gin.H{
			"page":       page,
			"page_size":  pageSize,
			"total":      total,
			"total_page": (total + int64(pageSize) - 1) / int64(pageSize),
		},
		"cached": cached,
	})
}

// GetPermissionsGrouped 获取分组的权限列表
// GET /api/v1/auth/admin/permissions/grouped
func (h *PermissionHandler) GetPermissionsGrouped(c *gin.Context) {
	// 调用Service层
	grouped, cached, err := h.permissionService.GetPermissionsGrouped()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取权限列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":   grouped,
		"cached": cached,
	})
}

// UpdatePermission 更新权限
// PUT /api/v1/auth/admin/permissions/:id
func (h *PermissionHandler) UpdatePermission(c *gin.Context) {
	permID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的权限ID",
		})
		return
	}

	var req models.UpdatePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	// 构建更新数据
	updates := map[string]interface{}{
		"display_name": req.DisplayName,
		"description":  req.Description,
	}

	// 调用Service层
	if err := h.permissionService.UpdatePermission(uint(permID), updates); err != nil {
		if err == services.ErrPermissionNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "权限不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "更新权限失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "权限更新成功",
	})
}

// DeletePermission 删除权限
// DELETE /api/v1/auth/admin/permissions/:id
func (h *PermissionHandler) DeletePermission(c *gin.Context) {
	permID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的权限ID",
		})
		return
	}

	// 调用Service层
	if err := h.permissionService.DeletePermission(uint(permID)); err != nil {
		if err == services.ErrPermissionNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "权限不存在",
			})
			return
		}
		if err == services.ErrPermissionInUse {
			c.JSON(http.StatusConflict, gin.H{
				"error": "权限正在使用中，无法删除",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "删除权限失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "权限删除成功",
	})
}

