package handlers

import (
	"intangible_cultural_heritage_backend/internal/models"
	"intangible_cultural_heritage_backend/internal/services"
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type HeritageHandler struct {
	heritageService *services.HeritageService
}

func NewHeritageHandler() *HeritageHandler {
	return &HeritageHandler{
		heritageService: services.NewHeritageService(),
	}
}

// 创建非遗项目
func (h *HeritageHandler) CreateHeritageItem(c *gin.Context) {
	userID, _ := c.Get("user_id")
	userRole, _ := c.Get("user_role")

	var req models.CreateHeritageItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	// 只有管理员和非遗人可以创建项目
	if userRole != "admin" && userRole != "non_heritage" {
		log.Printf("DEBUG: Permission denied for role: %s", userRole)
		c.J<PERSON>(http.StatusForbidden, gin.H{
			"error": "没有权限创建非遗项目，当前角色：" + userRole.(string),
		})
		return
	}

	heritageItem := &models.HeritageItem{
		Name:        req.Name,
		Description: req.Description,
		Category:    req.Category,
		Location:    req.Location,
		Latitude:    req.Latitude,
		Longitude:   req.Longitude,
		CoverImage:  req.CoverImage,
		Status:      "pending", // 默认待审核
		CreatedBy:   userID.(uint),
	}

	// 调用Service层
	if err := h.heritageService.CreateItem(heritageItem); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "创建非遗项目失败: " + err.Error(),
		})
		return
	}

	response := models.HeritageItemResponse{
		ID:          heritageItem.ID,
		Name:        heritageItem.Name,
		Description: heritageItem.Description,
		Category:    heritageItem.Category,
		Location:    heritageItem.Location,
		Latitude:    heritageItem.Latitude,
		Longitude:   heritageItem.Longitude,
		CoverImage:  heritageItem.CoverImage,
		Status:      heritageItem.Status,
		CreatedBy:   heritageItem.CreatedBy,
		CreatedAt:   heritageItem.CreatedAt,
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "非遗项目创建成功",
		"data":    response,
	})
}

// 获取所有非遗项目（公开接口）
func (h *HeritageHandler) GetAllHeritageItems(c *gin.Context) {
	category := c.Query("category")
	page := c.DefaultQuery("page", "1")
	pageNum, _ := strconv.Atoi(page)
	pageSize := 10

	// 调用Service层
	items, total, cached, err := h.heritageService.GetAllItems(category, pageNum, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取非遗项目失败: " + err.Error(),
		})
		return
	}

	// 转换为响应格式
	var responses []models.HeritageItemResponse
	for _, item := range items {
		responses = append(responses, models.HeritageItemResponse{
			ID:          item.ID,
			Name:        item.Name,
			Description: item.Description,
			Category:    item.Category,
			Location:    item.Location,
			Latitude:    item.Latitude,
			Longitude:   item.Longitude,
			CoverImage:  item.CoverImage,
			Status:      item.Status,
			CreatedBy:   item.CreatedBy,
			CreatedAt:   item.CreatedAt,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"data":   responses,
		"total":  total,
		"page":   pageNum,
		"cached": cached,
	})
}

// 根据ID获取非遗项目详情
func (h *HeritageHandler) GetHeritageItemByID(c *gin.Context) {
	id := c.Param("id")
	itemID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的项目ID",
		})
		return
	}

	// 调用Service层
	item, cached, err := h.heritageService.GetItemByID(uint(itemID))
	if err != nil {
		if err == services.ErrHeritageNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "非遗项目不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	// 获取浏览次数
	viewCount, _ := h.heritageService.GetViewCount(uint(itemID))

	response := models.HeritageItemResponse{
		ID:          item.ID,
		Name:        item.Name,
		Description: item.Description,
		Category:    item.Category,
		Location:    item.Location,
		Latitude:    item.Latitude,
		Longitude:   item.Longitude,
		CoverImage:  item.CoverImage,
		Status:      item.Status,
		ViewCount:   int(viewCount),
		CreatedBy:   item.CreatedBy,
		CreatedAt:   item.CreatedAt,
	}

	c.JSON(http.StatusOK, gin.H{
		"data":   response,
		"cached": cached,
	})
}

// 更新非遗项目（管理员和非遗人）
func (h *HeritageHandler) UpdateHeritageItem(c *gin.Context) {
	userID, _ := c.Get("user_id")
	userRole, _ := c.Get("user_role")
	id := c.Param("id")

	itemID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的项目ID",
		})
		return
	}

	// 先获取项目检查权限
	item, _, err := h.heritageService.GetItemByID(uint(itemID))
	if err != nil {
		if err == services.ErrHeritageNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "非遗项目不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	// 检查权限：管理员或创建者本人
	if userRole != "admin" && item.CreatedBy != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{
			"error": "没有权限修改此项目",
		})
		return
	}

	var req models.UpdateHeritageItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	// 构建更新数据
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.Category != "" {
		updates["category"] = req.Category
	}
	if req.Location != "" {
		updates["location"] = req.Location
	}
	if req.Latitude != 0 {
		updates["latitude"] = req.Latitude
	}
	if req.Longitude != 0 {
		updates["longitude"] = req.Longitude
	}
	if req.CoverImage != "" {
		updates["cover_image"] = req.CoverImage
	}
	// 只有管理员可以修改状态
	if req.Status != "" && userRole == "admin" {
		updates["status"] = req.Status
	}

	// 调用Service层更新
	if err := h.heritageService.UpdateItem(uint(itemID), updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "更新失败: " + err.Error(),
		})
		return
	}

	// 重新获取更新后的数据
	updatedItem, _, _ := h.heritageService.GetItemByID(uint(itemID))

	response := models.HeritageItemResponse{
		ID:          updatedItem.ID,
		Name:        updatedItem.Name,
		Description: updatedItem.Description,
		Category:    updatedItem.Category,
		Location:    updatedItem.Location,
		Latitude:    updatedItem.Latitude,
		Longitude:   updatedItem.Longitude,
		CoverImage:  updatedItem.CoverImage,
		Status:      updatedItem.Status,
		CreatedBy:   updatedItem.CreatedBy,
		CreatedAt:   updatedItem.CreatedAt,
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "非遗项目更新成功",
		"data":    response,
	})
}
