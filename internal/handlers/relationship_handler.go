package handlers

import (
	"fmt"
	"intangible_cultural_heritage_backend/internal/database"
	"intangible_cultural_heritage_backend/internal/models"
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
)

type RelationshipHandler struct {
}

func NewRelationshipHandler() *RelationshipHandler {
	return &RelationshipHandler{}
}

// 关注用户
func (h *RelationshipHandler) FollowUser(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req models.FollowRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	// 不能关注自己
	if userID.(uint) == req.FollowingID {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "不能关注自己",
		})
		return
	}

	// 检查被关注用户是否存在
	var targetUser models.User
	result := database.GetDB().First(&targetUser, req.FollowingID)
	if result.Error != nil {
		c.J<PERSON>(http.StatusNotFound, gin.H{
			"error": "用户不存在",
		})
		return
	}

	// 检查是否已经关注
	var existingRel models.UserRelationship
	result = database.GetDB().Where("follower_id = ? AND following_id = ?", userID, req.FollowingID).First(&existingRel)
	if result.RowsAffected > 0 {
		c.JSON(http.StatusConflict, gin.H{
			"error": "已经关注此用户",
		})
		return
	}

	// 创建关注关系
	relationship := models.UserRelationship{
		FollowerID:  userID.(uint),
		FollowingID: req.FollowingID,
	}

	result = database.GetDB().Create(&relationship)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "关注失败: " + result.Error.Error(),
		})
		return
	}

	// 获取用户信息
	var follower models.User
	var following models.User
	database.GetDB().First(&follower, userID)
	database.GetDB().First(&following, req.FollowingID)

	response := models.RelationshipResponse{
		ID:                relationship.ID,
		FollowerID:        relationship.FollowerID,
		FollowingID:       relationship.FollowingID,
		CreatedAt:         relationship.CreatedAt,
		FollowerUsername:  follower.Username,
		FollowerAvatar:    follower.AvatarURL,
		FollowingUsername: following.Username,
		FollowingAvatar:   following.AvatarURL,
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "关注成功",
		"data":    response,
	})
}

// 取消关注
func (h *RelationshipHandler) UnfollowUser(c *gin.Context) {
	userID, _ := c.Get("user_id")
	followingID := c.Param("user_id")

	// 查找关注关系
	var relationship models.UserRelationship
	result := database.GetDB().Where("follower_id = ? AND following_id = ?", userID, followingID).First(&relationship)
	if result.Error != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "未关注此用户",
		})
		return
	}

	// 删除关注关系
	database.GetDB().Delete(&relationship)

	c.JSON(http.StatusOK, gin.H{
		"message": "取消关注成功",
	})
}

// 获取粉丝列表
func (h *RelationshipHandler) GetFollowers(c *gin.Context) {
	userID := c.Param("user_id")

	var followers []models.UserRelationship
	result := database.GetDB().Where("following_id = ?", userID).Order("created_at DESC").Find(&followers)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取粉丝列表失败: " + result.Error.Error(),
		})
		return
	}

	var responses []models.RelationshipResponse
	for _, rel := range followers {
		// 获取用户信息
		var follower models.User
		var following models.User
		database.GetDB().First(&follower, rel.FollowerID)
		database.GetDB().First(&following, rel.FollowingID)

		response := models.RelationshipResponse{
			ID:                rel.ID,
			FollowerID:        rel.FollowerID,
			FollowingID:       rel.FollowingID,
			CreatedAt:         rel.CreatedAt,
			FollowerUsername:  follower.Username,
			FollowerAvatar:    follower.AvatarURL,
			FollowingUsername: following.Username,
			FollowingAvatar:   following.AvatarURL,
		}

		responses = append(responses, response)
	}

	c.JSON(http.StatusOK, gin.H{
		"data": responses,
	})
}

// 获取关注列表
func (h *RelationshipHandler) GetFollowing(c *gin.Context) {
	userID := c.Param("user_id")

	var following []models.UserRelationship
	result := database.GetDB().Where("follower_id = ?", userID).Order("created_at DESC").Find(&following)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取关注列表失败: " + result.Error.Error(),
		})
		return
	}

	var responses []models.RelationshipResponse
	for _, rel := range following {
		// 获取用户信息
		var follower models.User
		var followingUser models.User
		database.GetDB().First(&follower, rel.FollowerID)
		database.GetDB().First(&followingUser, rel.FollowingID)

		response := models.RelationshipResponse{
			ID:                rel.ID,
			FollowerID:        rel.FollowerID,
			FollowingID:       rel.FollowingID,
			CreatedAt:         rel.CreatedAt,
			FollowerUsername:  follower.Username,
			FollowerAvatar:    follower.AvatarURL,
			FollowingUsername: followingUser.Username,
			FollowingAvatar:   followingUser.AvatarURL,
		}

		responses = append(responses, response)
	}

	c.JSON(http.StatusOK, gin.H{
		"data": responses,
	})
}

// 获取用户统计信息
func (h *RelationshipHandler) GetUserStats(c *gin.Context) {
	userID := c.Param("user_id")

	// 转换userID为uint
	var targetUserID uint
	if _, err := fmt.Sscanf(userID, "%d", &targetUserID); err != nil {
		log.Printf("ERROR: Invalid user_id format: %s", userID)
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的用户ID",
		})
		return
	}

	var user models.User
	result := database.GetDB().First(&user, targetUserID)
	if result.Error != nil {
		log.Printf("ERROR: User not found - user_id: %d, error: %v", targetUserID, result.Error)
		c.JSON(http.StatusNotFound, gin.H{
			"error": "用户不存在",
		})
		return
	}

	log.Printf("DEBUG: Found user - ID: %d, Username: %s", user.ID, user.Username)

	stats := models.UserStats{
		UserID:    user.ID,
		Username:  user.Username,
		AvatarURL: user.AvatarURL,
	}

	// 统计发布内容数
	var postsCount int64
	if err := database.GetDB().Model(&models.UGCContent{}).Where("user_id = ? AND status = ?", targetUserID, "approved").Count(&postsCount).Error; err != nil {
		log.Printf("ERROR: Count posts failed - error: %v", err)
	} else {
		stats.PostsCount = int(postsCount)
	}

	// 统计打卡次数
	var checkinsCount int64
	if err := database.GetDB().Model(&models.CheckinRecord{}).Where("user_id = ?", targetUserID).Count(&checkinsCount).Error; err != nil {
		log.Printf("ERROR: Count checkins failed - error: %v", err)
	} else {
		stats.CheckinsCount = int(checkinsCount)
	}

	// 统计粉丝数
	var followersCount int64
	if err := database.GetDB().Model(&models.UserRelationship{}).Where("following_id = ?", targetUserID).Count(&followersCount).Error; err != nil {
		log.Printf("ERROR: Count followers failed - error: %v", err)
	} else {
		stats.FollowersCount = int(followersCount)
	}

	// 统计关注数
	var followingCount int64
	if err := database.GetDB().Model(&models.UserRelationship{}).Where("follower_id = ?", targetUserID).Count(&followingCount).Error; err != nil {
		log.Printf("ERROR: Count following failed - error: %v", err)
	} else {
		stats.FollowingCount = int(followingCount)
	}

	// 对于公开路由，检查是否有认证信息
	currentUserID, exists := c.Get("user_id")
	if exists && currentUserID != nil {
		// 有登录用户，检查关注关系
		if currentUserID.(uint) != targetUserID {
			var isFollowing int64
			if err := database.GetDB().Model(&models.UserRelationship{}).Where("follower_id = ? AND following_id = ?", currentUserID, targetUserID).Count(&isFollowing).Error; err != nil {
				log.Printf("ERROR: Check following status failed - error: %v", err)
			} else {
				stats.IsFollowing = isFollowing > 0
			}

			var isFollowed int64
			if err := database.GetDB().Model(&models.UserRelationship{}).Where("follower_id = ? AND following_id = ?", targetUserID, currentUserID).Count(&isFollowed).Error; err != nil {
				log.Printf("ERROR: Check followed status failed - error: %v", err)
			} else {
				stats.IsFollowed = isFollowed > 0
			}
		} else {
			// 查看自己的资料
			stats.IsFollowing = false
			stats.IsFollowed = false
		}
	} else {
		// 未登录用户，不显示关注关系
		stats.IsFollowing = false
		stats.IsFollowed = false
	}

	log.Printf("DEBUG: User stats - Posts: %d, Checkins: %d, Followers: %d, Following: %d",
		stats.PostsCount, stats.CheckinsCount, stats.FollowersCount, stats.FollowingCount)

	c.JSON(http.StatusOK, gin.H{
		"data": stats,
	})
}

// 获取用户主页信息
func (h *RelationshipHandler) GetUserProfile(c *gin.Context) {
	userID := c.Param("user_id")

	// 转换userID为uint
	var targetUserID uint
	if _, err := fmt.Sscanf(userID, "%d", &targetUserID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的用户ID",
		})
		return
	}

	var user models.User
	result := database.GetDB().First(&user, targetUserID)
	if result.Error != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "用户不存在",
		})
		return
	}

	profile := models.UserProfile{
		UserID:    user.ID,
		Username:  user.Username,
		Email:     user.Email,
		AvatarURL: user.AvatarURL,
		Role:      user.Role,
		CreatedAt: user.CreatedAt,
	}

	// 统计发布内容数
	var postsCount int64
	database.GetDB().Model(&models.UGCContent{}).Where("user_id = ? AND status = ?", targetUserID, "approved").Count(&postsCount)
	profile.PostsCount = int(postsCount)

	// 统计打卡次数
	var checkinsCount int64
	database.GetDB().Model(&models.CheckinRecord{}).Where("user_id = ?", targetUserID).Count(&checkinsCount)
	profile.CheckinsCount = int(checkinsCount)

	// 统计粉丝数
	var followersCount int64
	database.GetDB().Model(&models.UserRelationship{}).Where("following_id = ?", targetUserID).Count(&followersCount)
	profile.FollowersCount = int(followersCount)

	// 统计关注数
	var followingCount int64
	database.GetDB().Model(&models.UserRelationship{}).Where("follower_id = ?", targetUserID).Count(&followingCount)
	profile.FollowingCount = int(followingCount)

	// 检查是否有认证信息
	currentUserID, exists := c.Get("user_id")
	if exists && currentUserID != nil {
		profile.IsSelf = currentUserID.(uint) == targetUserID

		if !profile.IsSelf {
			var isFollowing int64
			database.GetDB().Model(&models.UserRelationship{}).Where("follower_id = ? AND following_id = ?", currentUserID, targetUserID).Count(&isFollowing)
			profile.IsFollowing = isFollowing > 0

			var isFollowed int64
			database.GetDB().Model(&models.UserRelationship{}).Where("follower_id = ? AND following_id = ?", targetUserID, currentUserID).Count(&isFollowed)
			profile.IsFollowed = isFollowed > 0
		}
	} else {
		profile.IsSelf = false
		profile.IsFollowing = false
		profile.IsFollowed = false
	}

	c.JSON(http.StatusOK, gin.H{
		"data": profile,
	})
}

// 检查关注状态
func (h *RelationshipHandler) CheckFollowStatus(c *gin.Context) {
	userID, _ := c.Get("user_id")
	targetUserID := c.Param("user_id")

	var count int64
	database.GetDB().Model(&models.UserRelationship{}).Where("follower_id = ? AND following_id = ?", userID, targetUserID).Count(&count)

	c.JSON(http.StatusOK, gin.H{
		"is_following": count > 0,
	})
}
