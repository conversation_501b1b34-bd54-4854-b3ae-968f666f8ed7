package handlers

import (
	"intangible_cultural_heritage_backend/internal/services"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// LocationHandler 地理位置处理器
type LocationHandler struct {
	service *services.LocationService
}

// NewLocationHandler 创建地理位置处理器实例
func NewLocationHandler() *LocationHandler {
	return &LocationHandler{
		service: services.NewLocationService(),
	}
}

// GetNearbyLocations 获取附近位置（使用PostGIS）
// @Summary 获取附近位置
// @Description 使用PostGIS查询附近的位置
// @Tags 地理位置
// @Accept json
// @Produce json
// @Param latitude query number true "纬度"
// @Param longitude query number true "经度"
// @Param radius query number false "半径（米），默认5000"
// @Param type query string false "位置类型"
// @Param limit query int false "返回数量，默认20"
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/locations/nearby [get]
func (h *LocationHandler) GetNearbyLocations(c *gin.Context) {
	var req services.NearbyLocationsRequest

	// 解析查询参数
	latStr := c.Query("latitude")
	lonStr := c.Query("longitude")
	radiusStr := c.DefaultQuery("radius", "5000")
	limitStr := c.DefaultQuery("limit", "20")

	// 转换参数
	lat, err := strconv.ParseFloat(latStr, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的纬度参数"})
		return
	}

	lon, err := strconv.ParseFloat(lonStr, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的经度参数"})
		return
	}

	radius, err := strconv.ParseFloat(radiusStr, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的半径参数"})
		return
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的limit参数"})
		return
	}

	req.Latitude = lat
	req.Longitude = lon
	req.Radius = radius
	req.Type = c.Query("type")
	req.Limit = limit

	// 调用服务层
	locations, err := h.service.GetNearbyLocations(&req)
	if err != nil {
		if err == services.ErrInvalidCoordinates {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "查询附近位置失败", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": gin.H{
			"locations": locations,
			"count":     len(locations),
			"radius":    radius,
		},
		"message": "查询成功",
	})
}

// GetLocationByID 获取位置详情
// @Summary 获取位置详情
// @Description 根据ID获取位置详情
// @Tags 地理位置
// @Produce json
// @Param id path int true "位置ID"
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/locations/{id} [get]
func (h *LocationHandler) GetLocationByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的位置ID"})
		return
	}

	location, err := h.service.GetLocationByID(uint(id))
	if err != nil {
		if err == services.ErrLocationNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "位置不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "查询位置失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"data":    location,
		"message": "查询成功",
	})
}

// GetLocationsList 获取位置列表
// @Summary 获取位置列表
// @Description 分页获取位置列表
// @Tags 地理位置
// @Produce json
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认10"
// @Param type query string false "位置类型"
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/locations [get]
func (h *LocationHandler) GetLocationsList(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	locationType := c.Query("type")

	locations, total, err := h.service.GetLocationsList(page, pageSize, locationType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "查询位置列表失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": gin.H{
			"locations": locations,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
		"message": "查询成功",
	})
}

// CreateLocation 创建位置
// @Summary 创建位置
// @Description 创建新的地理位置
// @Tags 地理位置
// @Accept json
// @Produce json
// @Param location body services.CreateLocationRequest true "位置信息"
// @Success 201 {object} map[string]interface{}
// @Router /api/v1/auth/locations [post]
func (h *LocationHandler) CreateLocation(c *gin.Context) {
	var req services.CreateLocationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求参数"})
		return
	}

	location, err := h.service.CreateLocation(req)
	if err != nil {
		if err == services.ErrInvalidCoordinates {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建位置失败"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"data":    location,
		"message": "创建成功",
	})
}

// UpdateLocation 更新位置
// @Summary 更新位置
// @Description 更新位置信息
// @Tags 地理位置
// @Accept json
// @Produce json
// @Param id path int true "位置ID"
// @Param location body services.UpdateLocationRequest true "位置信息"
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/auth/locations/{id} [put]
func (h *LocationHandler) UpdateLocation(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的位置ID"})
		return
	}

	var req services.UpdateLocationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求参数"})
		return
	}

	err = h.service.UpdateLocation(uint(id), req)
	if err != nil {
		if err == services.ErrLocationNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "位置不存在"})
			return
		}
		if err == services.ErrInvalidCoordinates {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新位置失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
	})
}

// DeleteLocation 删除位置
// @Summary 删除位置
// @Description 删除位置
// @Tags 地理位置
// @Produce json
// @Param id path int true "位置ID"
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/auth/admin/locations/{id} [delete]
func (h *LocationHandler) DeleteLocation(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的位置ID"})
		return
	}

	err = h.service.DeleteLocation(uint(id))
	if err != nil {
		if err == services.ErrLocationNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "位置不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除位置失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// GetHeatmapData 获取热力图数据
// @Summary 获取热力图数据
// @Description 获取地图热力图数据
// @Tags 地理位置
// @Accept json
// @Produce json
// @Param bounds body services.GeoBounds false "地理边界"
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/locations/heatmap [post]
func (h *LocationHandler) GetHeatmapData(c *gin.Context) {
	var bounds *services.GeoBounds
	if err := c.ShouldBindJSON(&bounds); err != nil {
		// 如果没有提供边界，查询所有数据
		bounds = nil
	}

	heatmapData, err := h.service.GetHeatmapData(bounds)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "查询热力图数据失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": gin.H{
			"points": heatmapData,
			"count":  len(heatmapData),
		},
		"message": "查询成功",
	})
}

// GetPopularLocations 获取热门地点
// @Summary 获取热门地点
// @Description 根据打卡次数获取热门地点
// @Tags 地理位置
// @Produce json
// @Param limit query int false "返回数量，默认10"
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/locations/popular [get]
func (h *LocationHandler) GetPopularLocations(c *gin.Context) {
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	locations, err := h.service.GetPopularLocations(limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "查询热门地点失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": gin.H{
			"locations": locations,
			"count":     len(locations),
		},
		"message": "查询成功",
	})
}
