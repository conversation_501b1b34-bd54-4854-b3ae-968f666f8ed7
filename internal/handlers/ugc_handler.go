package handlers

import (
	"encoding/json"
	"intangible_cultural_heritage_backend/internal/database"
	"intangible_cultural_heritage_backend/internal/models"
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
)

type UGCHandler struct {
}

func NewUGCHandler() *UGCHandler {
	return &UGCHandler{}
}

// 创建UGC内容
func (h *UGCHandler) CreateUGC(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req models.CreateUGCRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	// 验证内容类型
	if req.Type != "article" && req.Type != "photo" && req.Type != "video" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "内容类型必须是 article, photo 或 video",
		})
		return
	}

	// 转换媒体URL数组为JSON字符串
	mediaURLsJSON, err := json.Marshal(req.MediaURLs)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "媒体数据格式错误",
		})
		return
	}

	ugcContent := models.UGCContent{
		UserID:     userID.(uint),
		Title:      req.Title,
		Content:    req.Content,
		Type:       req.Type,
		MediaURLs:  string(mediaURLsJSON),
		LocationID: req.LocationID,
		Status:     "pending", // 默认待审核
	}

	result := database.GetDB().Create(&ugcContent)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "创建内容失败: " + result.Error.Error(),
		})
		return
	}

	// 获取用户和地点信息
	var user models.User
	var location models.Location
	database.GetDB().First(&user, userID)
	if req.LocationID > 0 {
		database.GetDB().First(&location, req.LocationID)
	}

	// 解析媒体URL JSON
	var mediaURLs []string
	json.Unmarshal([]byte(ugcContent.MediaURLs), &mediaURLs)

	response := models.UGCResponse{
		ID:           ugcContent.ID,
		UserID:       ugcContent.UserID,
		Title:        ugcContent.Title,
		Content:      ugcContent.Content,
		Type:         ugcContent.Type,
		MediaURLs:    mediaURLs,
		LocationID:   ugcContent.LocationID,
		Status:       ugcContent.Status,
		CreatedAt:    ugcContent.CreatedAt,
		UserName:     user.Username,
		UserAvatar:   user.AvatarURL,
		LocationName: location.Name,
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "内容创建成功，等待审核",
		"data":    response,
	})
}

// 获取所有UGC内容（公开，只返回已审核的）
func (h *UGCHandler) GetAllUGC(c *gin.Context) {
	var ugcContents []models.UGCContent
	// 添加SQL调试
	log.Printf("DEBUG: Executing SQL: SELECT * FROM ugc_contents WHERE status = 'approved'")
	// 只返回已审核通过的内容
	result := database.GetDB().Where("status = ?", "approved").Order("created_at DESC").Find(&ugcContents)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取内容失败: " + result.Error.Error(),
		})
		return
	}
	// 添加调试信息
	log.Printf("DEBUG: SQL result error: %v", result.Error)
	log.Printf("DEBUG: Found %d UGC contents", len(ugcContents))
	if result.Error != nil {
		log.Printf("ERROR: Database query failed: %v", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取内容失败: " + result.Error.Error(),
		})
		return
	}

	// 调试每个找到的内容
	for i, ugc := range ugcContents {
		log.Printf("DEBUG: UGC %d - ID: %d, Title: %s, Status: %s", i, ugc.ID, ugc.Title, ugc.Status)
	}
	var responses []models.UGCResponse
	for _, ugc := range ugcContents {
		// 获取用户信息
		var user models.User
		userResult := database.GetDB().First(&user, ugc.UserID)
		if userResult.Error != nil {
			log.Printf("WARNING: User not found for UGC %d: %v", ugc.ID, userResult.Error)
			continue
		}
		// 获取地点信息
		var location models.Location
		if ugc.LocationID > 0 {
			locationResult := database.GetDB().First(&location, ugc.LocationID)
			if locationResult.Error != nil {
				log.Printf("WARNING: Location not found for UGC %d: %v", ugc.ID, locationResult.Error)
			}
		}

		// 解析媒体URL JSON
		var mediaURLs []string
		if ugc.MediaURLs != "" {
			if err := json.Unmarshal([]byte(ugc.MediaURLs), &mediaURLs); err != nil {
				log.Printf("WARNING: Failed to parse media URLs for UGC %d: %v", ugc.ID, err)
				mediaURLs = []string{}
			}
		}

		response := models.UGCResponse{
			ID:           ugc.ID,
			UserID:       ugc.UserID,
			Title:        ugc.Title,
			Content:      ugc.Content,
			Type:         ugc.Type,
			MediaURLs:    mediaURLs,
			LocationID:   ugc.LocationID,
			Status:       ugc.Status,
			CreatedAt:    ugc.CreatedAt,
			UserName:     user.Username,
			UserAvatar:   user.AvatarURL,
			LocationName: location.Name,
		}

		responses = append(responses, response)
	}

	log.Printf("DEBUG: Returning %d responses", len(responses))

	c.JSON(http.StatusOK, gin.H{
		"data": responses,
	})
}

// 获取用户自己的UGC内容
func (h *UGCHandler) GetUserUGC(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var ugcContents []models.UGCContent
	result := database.GetDB().Where("user_id = ?", userID).Order("created_at DESC").Find(&ugcContents)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取内容失败: " + result.Error.Error(),
		})
		return
	}

	var responses []models.UGCResponse
	for _, ugc := range ugcContents {
		// 获取用户信息
		var user models.User
		database.GetDB().First(&user, ugc.UserID)

		// 获取地点信息
		var location models.Location
		if ugc.LocationID > 0 {
			database.GetDB().First(&location, ugc.LocationID)
		}

		// 解析媒体URL JSON
		var mediaURLs []string
		json.Unmarshal([]byte(ugc.MediaURLs), &mediaURLs)

		responses = append(responses, models.UGCResponse{
			ID:           ugc.ID,
			UserID:       ugc.UserID,
			Title:        ugc.Title,
			Content:      ugc.Content,
			Type:         ugc.Type,
			MediaURLs:    mediaURLs,
			LocationID:   ugc.LocationID,
			Status:       ugc.Status,
			CreatedAt:    ugc.CreatedAt,
			UserName:     user.Username,
			UserAvatar:   user.AvatarURL,
			LocationName: location.Name,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"data": responses,
	})
}

// 根据类型获取UGC内容
func (h *UGCHandler) GetUGCByType(c *gin.Context) {
	contentType := c.Param("type")

	// 验证内容类型
	if contentType != "article" && contentType != "photo" && contentType != "video" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "内容类型必须是 article, photo 或 video",
		})
		return
	}

	var ugcContents []models.UGCContent
	result := database.GetDB().Where("type = ? AND status = ?", contentType, "approved").Order("created_at DESC").Find(&ugcContents)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取内容失败: " + result.Error.Error(),
		})
		return
	}

	var responses []models.UGCResponse
	for _, ugc := range ugcContents {
		// 获取用户信息
		var user models.User
		database.GetDB().First(&user, ugc.UserID)

		// 获取地点信息
		var location models.Location
		if ugc.LocationID > 0 {
			database.GetDB().First(&location, ugc.LocationID)
		}

		// 解析媒体URL JSON
		var mediaURLs []string
		json.Unmarshal([]byte(ugc.MediaURLs), &mediaURLs)

		responses = append(responses, models.UGCResponse{
			ID:           ugc.ID,
			UserID:       ugc.UserID,
			Title:        ugc.Title,
			Content:      ugc.Content,
			Type:         ugc.Type,
			MediaURLs:    mediaURLs,
			LocationID:   ugc.LocationID,
			Status:       ugc.Status,
			CreatedAt:    ugc.CreatedAt,
			UserName:     user.Username,
			UserAvatar:   user.AvatarURL,
			LocationName: location.Name,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"data": responses,
	})
}

// 审核UGC内容（管理员）
func (h *UGCHandler) ReviewUGC(c *gin.Context) {
	userRole, _ := c.Get("user_role")

	// 只有管理员可以审核内容
	if userRole != "admin" {
		c.JSON(http.StatusForbidden, gin.H{
			"error": "没有权限审核内容",
		})
		return
	}

	contentID := c.Param("id")
	var req models.UpdateUGCRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	var ugcContent models.UGCContent
	result := database.GetDB().First(&ugcContent, contentID)
	if result.Error != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "内容不存在",
		})
		return
	}

	// 更新状态
	if req.Status != "" {
		ugcContent.Status = req.Status
	}

	database.GetDB().Save(&ugcContent)

	// 获取用户和地点信息
	var user models.User
	var location models.Location
	database.GetDB().First(&user, ugcContent.UserID)
	if ugcContent.LocationID > 0 {
		database.GetDB().First(&location, ugcContent.LocationID)
	}

	// 解析媒体URL JSON
	var mediaURLs []string
	json.Unmarshal([]byte(ugcContent.MediaURLs), &mediaURLs)

	response := models.UGCResponse{
		ID:           ugcContent.ID,
		UserID:       ugcContent.UserID,
		Title:        ugcContent.Title,
		Content:      ugcContent.Content,
		Type:         ugcContent.Type,
		MediaURLs:    mediaURLs,
		LocationID:   ugcContent.LocationID,
		Status:       ugcContent.Status,
		CreatedAt:    ugcContent.CreatedAt,
		UserName:     user.Username,
		UserAvatar:   user.AvatarURL,
		LocationName: location.Name,
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "内容审核完成",
		"data":    response,
	})
}

// 获取UGC内容统计
func (h *UGCHandler) GetUGCStats(c *gin.Context) {
	userRole, _ := c.Get("user_role")

	// 只有管理员可以查看统计
	if userRole != "admin" {
		c.JSON(http.StatusForbidden, gin.H{
			"error": "没有权限查看统计",
		})
		return
	}

	stats := models.UGCStats{}

	// 总内容数
	var totalCount int64
	database.GetDB().Model(&models.UGCContent{}).Count(&totalCount)
	stats.TotalContents = int(totalCount)

	// 各类型内容数
	var articlesCount int64
	database.GetDB().Model(&models.UGCContent{}).Where("type = ?", "article").Count(&articlesCount)
	stats.ArticlesCount = int(articlesCount)

	var photosCount int64
	database.GetDB().Model(&models.UGCContent{}).Where("type = ?", "photo").Count(&photosCount)
	stats.PhotosCount = int(photosCount)

	var videosCount int64
	database.GetDB().Model(&models.UGCContent{}).Where("type = ?", "video").Count(&videosCount)
	stats.VideosCount = int(videosCount)

	// 各状态内容数
	var approvedCount int64
	database.GetDB().Model(&models.UGCContent{}).Where("status = ?", "approved").Count(&approvedCount)
	stats.ApprovedCount = int(approvedCount)

	var pendingCount int64
	database.GetDB().Model(&models.UGCContent{}).Where("status = ?", "pending").Count(&pendingCount)
	stats.PendingCount = int(pendingCount)

	c.JSON(http.StatusOK, gin.H{
		"data": stats,
	})
}
