package handlers

import (
	"intangible_cultural_heritage_backend/internal/models"
	"intangible_cultural_heritage_backend/internal/services"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// AchievementHandler 成就处理器
type AchievementHandler struct {
	achievementService *services.AchievementService
}

// NewAchievementHandler 创建成就处理器实例
func NewAchievementHandler() *AchievementHandler {
	return &AchievementHandler{
		achievementService: services.NewAchievementService(),
	}
}

// GetAchievements 获取成就列表
// @Summary 获取成就列表
// @Tags 成就
// @Accept json
// @Produce json
// @Param category query string false "成就分类"
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/achievements [get]
func (h *AchievementHandler) GetAchievements(c *gin.Context) {
	category := c.Query("category")

	achievements, err := h.achievementService.GetAchievements(category)
	if err != nil {
		c.<PERSON>SO<PERSON>(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": achievements,
	})
}

// GetUserAchievements 获取用户成就列表
// @Summary 获取用户成就列表
// @Tags 成就
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param is_completed query bool false "是否已完成"
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/auth/achievements/my [get]
func (h *AchievementHandler) GetUserAchievements(c *gin.Context) {
	userID := c.GetUint("user_id")

	var isCompleted *bool
	if c.Query("is_completed") != "" {
		completed := c.Query("is_completed") == "true"
		isCompleted = &completed
	}

	userAchievements, err := h.achievementService.GetUserAchievements(userID, isCompleted)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": userAchievements,
	})
}

// GetUserAchievementStats 获取用户成就统计
// @Summary 获取用户成就统计
// @Tags 成就
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/auth/achievements/stats [get]
func (h *AchievementHandler) GetUserAchievementStats(c *gin.Context) {
	userID := c.GetUint("user_id")

	stats, err := h.achievementService.GetUserAchievementStats(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": stats,
	})
}

// CreateAchievement 创建成就（管理员）
// @Summary 创建成就
// @Tags 成就管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param achievement body models.Achievement true "成就信息"
// @Success 201 {object} map[string]interface{}
// @Router /api/v1/admin/achievements [post]
func (h *AchievementHandler) CreateAchievement(c *gin.Context) {
	var achievement models.Achievement
	if err := c.ShouldBindJSON(&achievement); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求参数"})
		return
	}

	if err := h.achievementService.CreateAchievement(&achievement); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "成就创建成功",
		"data":    achievement,
	})
}

// UpdateAchievement 更新成就（管理员）
// @Summary 更新成就
// @Tags 成就管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "成就ID"
// @Param updates body map[string]interface{} true "更新内容"
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/admin/achievements/:id [put]
func (h *AchievementHandler) UpdateAchievement(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的成就ID"})
		return
	}

	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求参数"})
		return
	}

	if err := h.achievementService.UpdateAchievement(uint(id), updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "成就更新成功",
	})
}

// DeleteAchievement 删除成就（管理员）
// @Summary 删除成就
// @Tags 成就管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "成就ID"
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/admin/achievements/:id [delete]
func (h *AchievementHandler) DeleteAchievement(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的成就ID"})
		return
	}

	if err := h.achievementService.DeleteAchievement(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "成就删除成功",
	})
}

