package cache

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

var (
	client *redis.Client
	ctx    = context.Background()
)

// RedisConfig Redis配置
type RedisConfig struct {
	Addr     string
	Password string
	DB       int
}

// Init 初始化Redis连接
func Init(config RedisConfig) error {
	client = redis.NewClient(&redis.Options{
		Addr:         config.Addr,
		Password:     config.Password,
		DB:           config.DB,
		PoolSize:     10,              // 连接池大小
		MinIdleConns: 5,               // 最小空闲连接数
		MaxRetries:   3,               // 最大重试次数
		DialTimeout:  5 * time.Second, // 连接超时
		ReadTimeout:  3 * time.Second, // 读取超时
		WriteTimeout: 3 * time.Second, // 写入超时
		PoolTimeout:  4 * time.Second, // 连接池超时
	})

	// 测试连接
	if err := client.Ping(ctx).Err(); err != nil {
		return fmt.Errorf("Redis连接失败: %v", err)
	}

	fmt.Println("✅ Redis连接成功")
	return nil
}

// GetClient 获取Redis客户端
func GetClient() *redis.Client {
	return client
}

// Close 关闭Redis连接
func Close() error {
	if client != nil {
		return client.Close()
	}
	return nil
}

// Set 设置缓存（带过期时间）
func Set(key string, value interface{}, expiration time.Duration) error {
	return client.Set(ctx, key, value, expiration).Err()
}

// Get 获取缓存
func Get(key string) (string, error) {
	return client.Get(ctx, key).Result()
}

// GetBytes 获取缓存（字节）
func GetBytes(key string) ([]byte, error) {
	return client.Get(ctx, key).Bytes()
}

// Delete 删除缓存
func Delete(keys ...string) error {
	return client.Del(ctx, keys...).Err()
}

// Exists 检查key是否存在
func Exists(keys ...string) (int64, error) {
	return client.Exists(ctx, keys...).Result()
}

// Expire 设置过期时间
func Expire(key string, expiration time.Duration) error {
	return client.Expire(ctx, key, expiration).Err()
}

// TTL 获取剩余过期时间
func TTL(key string) (time.Duration, error) {
	return client.TTL(ctx, key).Result()
}

// Incr 自增
func Incr(key string) (int64, error) {
	return client.Incr(ctx, key).Result()
}

// Decr 自减
func Decr(key string) (int64, error) {
	return client.Decr(ctx, key).Result()
}

// HSet 设置Hash字段
func HSet(key string, field string, value interface{}) error {
	return client.HSet(ctx, key, field, value).Err()
}

// HGet 获取Hash字段
func HGet(key string, field string) (string, error) {
	return client.HGet(ctx, key, field).Result()
}

// HGetAll 获取Hash所有字段
func HGetAll(key string) (map[string]string, error) {
	return client.HGetAll(ctx, key).Result()
}

// HDel 删除Hash字段
func HDel(key string, fields ...string) error {
	return client.HDel(ctx, key, fields...).Err()
}

// SAdd 添加到Set
func SAdd(key string, members ...interface{}) error {
	return client.SAdd(ctx, key, members...).Err()
}

// SMembers 获取Set所有成员
func SMembers(key string) ([]string, error) {
	return client.SMembers(ctx, key).Result()
}

// SIsMember 检查是否是Set成员
func SIsMember(key string, member interface{}) (bool, error) {
	return client.SIsMember(ctx, key, member).Result()
}

// SRem 从Set中移除
func SRem(key string, members ...interface{}) error {
	return client.SRem(ctx, key, members...).Err()
}

// ZAdd 添加到有序集合
func ZAdd(key string, score float64, member interface{}) error {
	return client.ZAdd(ctx, key, redis.Z{
		Score:  score,
		Member: member,
	}).Err()
}

// ZRange 获取有序集合范围（按分数从小到大）
func ZRange(key string, start, stop int64) ([]string, error) {
	return client.ZRange(ctx, key, start, stop).Result()
}

// ZRevRange 获取有序集合范围（按分数从大到小）
func ZRevRange(key string, start, stop int64) ([]string, error) {
	return client.ZRevRange(ctx, key, start, stop).Result()
}

// ZRem 从有序集合中移除
func ZRem(key string, members ...interface{}) error {
	return client.ZRem(ctx, key, members...).Err()
}

// FlushDB 清空当前数据库（谨慎使用）
func FlushDB() error {
	return client.FlushDB(ctx).Err()
}

// Keys 获取匹配的所有key（生产环境慎用）
func Keys(pattern string) ([]string, error) {
	return client.Keys(ctx, pattern).Result()
}

// ==================== 业务专用方法 ====================

// SetJSON 设置JSON缓存
func SetJSON(key string, value interface{}, expiration time.Duration) error {
	// 这里可以使用 json.Marshal 序列化
	// 为了简化，先使用字符串
	return Set(key, value, expiration)
}

// GetJSON 获取JSON缓存
func GetJSON(key string) (string, error) {
	return Get(key)
}

// SetTokenBlacklist 将Token加入黑名单
func SetTokenBlacklist(token string, expiration time.Duration) error {
	key := fmt.Sprintf("token:blacklist:%s", token)
	return Set(key, "1", expiration)
}

// IsTokenBlacklisted 检查Token是否在黑名单
func IsTokenBlacklisted(token string) (bool, error) {
	key := fmt.Sprintf("token:blacklist:%s", token)
	count, err := Exists(key)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// SetUserSession 设置用户会话
func SetUserSession(userID uint, token string, expiration time.Duration) error {
	key := fmt.Sprintf("user:session:%d", userID)
	return Set(key, token, expiration)
}

// GetUserSession 获取用户会话
func GetUserSession(userID uint) (string, error) {
	key := fmt.Sprintf("user:session:%d", userID)
	return Get(key)
}

// DeleteUserSession 删除用户会话
func DeleteUserSession(userID uint) error {
	key := fmt.Sprintf("user:session:%d", userID)
	return Delete(key)
}

// CacheHeritageItems 缓存热门非遗项目列表
func CacheHeritageItems(items interface{}, expiration time.Duration) error {
	key := "heritage:items:hot"
	return SetJSON(key, items, expiration)
}

// GetCachedHeritageItems 获取缓存的非遗项目列表
func GetCachedHeritageItems() (string, error) {
	key := "heritage:items:hot"
	return GetJSON(key)
}

// IncrViewCount 增加浏览次数
func IncrViewCount(itemType string, itemID uint) (int64, error) {
	key := fmt.Sprintf("%s:view:%d", itemType, itemID)
	return Incr(key)
}

// GetViewCount 获取浏览次数
func GetViewCount(itemType string, itemID uint) (string, error) {
	key := fmt.Sprintf("%s:view:%d", itemType, itemID)
	return Get(key)
}
