package cache

import (
	"fmt"
	"time"
)

// ==================== 缓存键常量 ====================

const (
	// 缓存前缀
	PrefixUser       = "user"
	PrefixHeritage   = "heritage"
	PrefixLocation   = "location"
	PrefixUGC        = "ugc"
	PrefixComment    = "comment"
	PrefixRole       = "role"
	PrefixPermission = "permission"
	PrefixStats      = "stats"
	PrefixToken      = "token"
)

// ==================== 缓存过期时间 ====================

const (
	// 短期缓存（5分钟）
	ExpireShort = 5 * time.Minute

	// 中期缓存（30分钟）
	ExpireMedium = 30 * time.Minute

	// 长期缓存（1小时）
	ExpireLong = 1 * time.Hour

	// 超长期缓存（24小时）
	ExpireVeryLong = 24 * time.Hour

	// 统计数据缓存（5分钟）
	ExpireStats = 5 * time.Minute

	// 热门数据缓存（15分钟）
	ExpireHot = 15 * time.Minute
)

// ==================== 用户相关缓存键 ====================

// UserInfoKey 用户信息缓存键
func UserInfoKey(userID uint) string {
	return fmt.Sprintf("%s:info:%d", PrefixUser, userID)
}

// UserEmailKey 用户邮箱缓存键
func UserEmailKey(email string) string {
	return fmt.Sprintf("%s:email:%s", PrefixUser, email)
}

// UserSessionKey 用户会话缓存键
func UserSessionKey(userID uint) string {
	return fmt.Sprintf("%s:session:%d", PrefixUser, userID)
}

// UserRolesKey 用户角色缓存键
func UserRolesKey(userID uint) string {
	return fmt.Sprintf("%s:roles:%d", PrefixUser, userID)
}

// UserPermissionsKey 用户权限缓存键
func UserPermissionsKey(userID uint) string {
	return fmt.Sprintf("%s:permissions:%d", PrefixUser, userID)
}

// ==================== 非遗项目相关缓存键 ====================

// HeritageItemKey 非遗项目详情缓存键
func HeritageItemKey(itemID uint) string {
	return fmt.Sprintf("%s:item:%d", PrefixHeritage, itemID)
}

// HeritageListKey 非遗项目列表缓存键
func HeritageListKey(category string, page int) string {
	if category == "" {
		category = "all"
	}
	return fmt.Sprintf("%s:list:%s:page:%d", PrefixHeritage, category, page)
}

// HeritageHotKey 热门非遗项目缓存键
func HeritageHotKey() string {
	return fmt.Sprintf("%s:hot", PrefixHeritage)
}

// HeritageViewCountKey 非遗项目浏览次数缓存键
func HeritageViewCountKey(itemID uint) string {
	return fmt.Sprintf("%s:view:%d", PrefixHeritage, itemID)
}

// ==================== 地点相关缓存键 ====================

// LocationKey 地点详情缓存键
func LocationKey(locationID uint) string {
	return fmt.Sprintf("%s:info:%d", PrefixLocation, locationID)
}

// LocationListKey 地点列表缓存键
func LocationListKey(page int) string {
	return fmt.Sprintf("%s:list:page:%d", PrefixLocation, page)
}

// LocationNearbyKey 附近地点缓存键
func LocationNearbyKey(lat, lng float64, radius int) string {
	return fmt.Sprintf("%s:nearby:%.6f:%.6f:%d", PrefixLocation, lat, lng, radius)
}

// ==================== UGC内容相关缓存键 ====================

// UGCContentKey UGC内容详情缓存键
func UGCContentKey(contentID uint) string {
	return fmt.Sprintf("%s:content:%d", PrefixUGC, contentID)
}

// UGCListKey UGC内容列表缓存键
func UGCListKey(page int) string {
	return fmt.Sprintf("%s:list:page:%d", PrefixUGC, page)
}

// UGCHotKey 热门UGC内容缓存键
func UGCHotKey() string {
	return fmt.Sprintf("%s:hot", PrefixUGC)
}

// ==================== 评论相关缓存键 ====================

// CommentListKey 评论列表缓存键
func CommentListKey(targetType string, targetID uint, page int) string {
	return fmt.Sprintf("%s:list:%s:%d:page:%d", PrefixComment, targetType, targetID, page)
}

// CommentCountKey 评论数量缓存键
func CommentCountKey(targetType string, targetID uint) string {
	return fmt.Sprintf("%s:count:%s:%d", PrefixComment, targetType, targetID)
}

// ==================== 角色权限相关缓存键 ====================

// RoleKey 角色详情缓存键
func RoleKey(roleID uint) string {
	return fmt.Sprintf("%s:info:%d", PrefixRole, roleID)
}

// RoleListKey 角色列表缓存键
func RoleListKey() string {
	return fmt.Sprintf("%s:list", PrefixRole)
}

// RolePermissionsKey 角色权限缓存键
func RolePermissionsKey(roleID uint) string {
	return fmt.Sprintf("%s:permissions:%d", PrefixRole, roleID)
}

// PermissionKey 权限详情缓存键
func PermissionKey(permissionID uint) string {
	return fmt.Sprintf("%s:info:%d", PrefixPermission, permissionID)
}

// PermissionListKey 权限列表缓存键
func PermissionListKey() string {
	return fmt.Sprintf("%s:list", PrefixPermission)
}

// PermissionGroupedKey 分组权限列表缓存键
func PermissionGroupedKey() string {
	return fmt.Sprintf("%s:grouped", PrefixPermission)
}

// ==================== 统计相关缓存键 ====================

// StatsOverviewKey 平台概览统计缓存键
func StatsOverviewKey() string {
	return fmt.Sprintf("%s:overview", PrefixStats)
}

// StatsUserGrowthKey 用户增长统计缓存键
func StatsUserGrowthKey(days int) string {
	return fmt.Sprintf("%s:user:growth:%d", PrefixStats, days)
}

// StatsContentRankKey 内容排行榜缓存键
func StatsContentRankKey(rankType string, limit int) string {
	return fmt.Sprintf("%s:rank:%s:%d", PrefixStats, rankType, limit)
}

// ==================== Token相关缓存键 ====================

// TokenBlacklistKey Token黑名单缓存键
func TokenBlacklistKey(token string) string {
	return fmt.Sprintf("%s:blacklist:%s", PrefixToken, token)
}

// ==================== 缓存管理器实例 ====================

var (
	// UserCache 用户缓存管理器
	UserCache = NewCacheManager(PrefixUser)

	// HeritageCache 非遗项目缓存管理器
	HeritageCache = NewCacheManager(PrefixHeritage)

	// LocationCache 地点缓存管理器
	LocationCache = NewCacheManager(PrefixLocation)

	// UGCCache UGC内容缓存管理器
	UGCCache = NewCacheManager(PrefixUGC)

	// CommentCache 评论缓存管理器
	CommentCache = NewCacheManager(PrefixComment)

	// RoleCache 角色缓存管理器
	RoleCache = NewCacheManager(PrefixRole)

	// PermissionCache 权限缓存管理器
	PermissionCache = NewCacheManager(PrefixPermission)

	// StatsCache 统计缓存管理器
	StatsCache = NewCacheManager(PrefixStats)
)
