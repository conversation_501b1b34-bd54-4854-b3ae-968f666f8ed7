package cache

import (
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

import (
	"encoding/json"
)

// ==================== 缓存管理器 ====================

// CacheManager 缓存管理器
type CacheManager struct {
	prefix string
}

// NewCacheManager 创建缓存管理器
func NewCacheManager(prefix string) *CacheManager {
	return &CacheManager{
		prefix: prefix,
	}
}

// buildKey 构建完整的缓存键
func (cm *CacheManager) buildKey(key string) string {
	return fmt.Sprintf("%s:%s", cm.prefix, key)
}

// SetObject 设置对象缓存（自动JSON序列化）
func (cm *CacheManager) SetObject(key string, value interface{}, expiration time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("序列化失败: %v", err)
	}
	return Set(cm.buildKey(key), string(data), expiration)
}

// GetObject 获取对象缓存（自动JSON反序列化）
func (cm *CacheManager) GetObject(key string, dest interface{}) error {
	data, err := Get(cm.buildKey(key))
	if err != nil {
		if err == redis.Nil {
			return ErrCacheMiss
		}
		return err
	}

	return json.Unmarshal([]byte(data), dest)
}

// Set 设置缓存
func (cm *CacheManager) Set(key string, value interface{}, expiration time.Duration) error {
	return Set(cm.buildKey(key), value, expiration)
}

// Get 获取缓存
func (cm *CacheManager) Get(key string) (string, error) {
	return Get(cm.buildKey(key))
}

// Delete 删除缓存
func (cm *CacheManager) Delete(keys ...string) error {
	fullKeys := make([]string, len(keys))
	for i, key := range keys {
		fullKeys[i] = cm.buildKey(key)
	}
	return Delete(fullKeys...)
}

// DeletePattern 删除匹配模式的所有缓存
func (cm *CacheManager) DeletePattern(pattern string) error {
	fullPattern := cm.buildKey(pattern)
	keys, err := Keys(fullPattern)
	if err != nil {
		return err
	}

	if len(keys) == 0 {
		return nil
	}

	return Delete(keys...)
}

// Exists 检查缓存是否存在
func (cm *CacheManager) Exists(key string) (bool, error) {
	count, err := Exists(cm.buildKey(key))
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// ==================== 缓存错误 ====================

var (
	ErrCacheMiss = fmt.Errorf("缓存未命中")
)

// ==================== 缓存装饰器 ====================

// GetOrSet 获取缓存，如果不存在则执行函数并缓存结果
func (cm *CacheManager) GetOrSet(key string, expiration time.Duration, fn func() (interface{}, error)) (interface{}, error) {
	// 先尝试从缓存获取
	var result interface{}
	err := cm.GetObject(key, &result)
	if err == nil {
		return result, nil
	}

	// 缓存未命中，执行函数
	if err == ErrCacheMiss || err == redis.Nil {
		data, err := fn()
		if err != nil {
			return nil, err
		}

		// 缓存结果
		if err := cm.SetObject(key, data, expiration); err != nil {
			// 缓存失败不影响业务，只记录日志
			fmt.Printf("⚠️  缓存设置失败: %v\n", err)
		}

		return data, nil
	}

	return nil, err
}

// ==================== 批量操作 ====================

// MGet 批量获取缓存
func (cm *CacheManager) MGet(keys ...string) ([]interface{}, error) {
	fullKeys := make([]string, len(keys))
	for i, key := range keys {
		fullKeys[i] = cm.buildKey(key)
	}

	return client.MGet(ctx, fullKeys...).Result()
}

// MSet 批量设置缓存
func (cm *CacheManager) MSet(pairs map[string]interface{}, expiration time.Duration) error {
	pipe := client.Pipeline()

	for key, value := range pairs {
		fullKey := cm.buildKey(key)
		pipe.Set(ctx, fullKey, value, expiration)
	}

	_, err := pipe.Exec(ctx)
	return err
}

// ==================== 计数器操作 ====================

// Incr 自增
func (cm *CacheManager) Incr(key string) (int64, error) {
	return Incr(cm.buildKey(key))
}

// IncrBy 增加指定值
func (cm *CacheManager) IncrBy(key string, value int64) (int64, error) {
	return client.IncrBy(ctx, cm.buildKey(key), value).Result()
}

// Decr 自减
func (cm *CacheManager) Decr(key string) (int64, error) {
	return Decr(cm.buildKey(key))
}

// ==================== 列表操作 ====================

// LPush 从左侧推入列表
func (cm *CacheManager) LPush(key string, values ...interface{}) error {
	return client.LPush(ctx, cm.buildKey(key), values...).Err()
}

// RPush 从右侧推入列表
func (cm *CacheManager) RPush(key string, values ...interface{}) error {
	return client.RPush(ctx, cm.buildKey(key), values...).Err()
}

// LRange 获取列表范围
func (cm *CacheManager) LRange(key string, start, stop int64) ([]string, error) {
	return client.LRange(ctx, cm.buildKey(key), start, stop).Result()
}

// LLen 获取列表长度
func (cm *CacheManager) LLen(key string) (int64, error) {
	return client.LLen(ctx, cm.buildKey(key)).Result()
}

// ==================== 有序集合操作 ====================

// ZAddWithScore 添加到有序集合（带分数）
func (cm *CacheManager) ZAddWithScore(key string, score float64, member interface{}) error {
	return ZAdd(cm.buildKey(key), score, member)
}

// ZRevRangeWithScores 获取有序集合（倒序，带分数）
func (cm *CacheManager) ZRevRangeWithScores(key string, start, stop int64) ([]redis.Z, error) {
	return client.ZRevRangeWithScores(ctx, cm.buildKey(key), start, stop).Result()
}

// ZIncrBy 增加有序集合成员的分数
func (cm *CacheManager) ZIncrBy(key string, increment float64, member string) (float64, error) {
	return client.ZIncrBy(ctx, cm.buildKey(key), increment, member).Result()
}
