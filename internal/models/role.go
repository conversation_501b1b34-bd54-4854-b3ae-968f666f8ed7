package models

import (
	"time"
)

// ============================================
// 角色模型
// ============================================

// Role 角色
type Role struct {
	ID          uint      `gorm:"primaryKey;column:id" json:"id"`
	Name        string    `gorm:"column:name;uniqueIndex;not null" json:"name"`
	DisplayName string    `gorm:"column:display_name;not null" json:"display_name"`
	Description string    `gorm:"column:description" json:"description"`
	IsSystem    bool      `gorm:"column:is_system;default:false" json:"is_system"`
	CreatedAt   time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`

	// 关联
	Permissions []Permission `gorm:"many2many:role_permissions;" json:"permissions,omitempty"`
}

func (Role) TableName() string {
	return "roles"
}

// ============================================
// 用户角色关联
// ============================================

// UserRole 用户角色关联
type UserRole struct {
	ID        uint      `gorm:"primaryKey;column:id" json:"id"`
	UserID    uint      `gorm:"column:user_id;not null" json:"user_id"`
	RoleID    uint      `gorm:"column:role_id;not null" json:"role_id"`
	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`

	// 关联
	User User `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Role Role `gorm:"foreignKey:RoleID" json:"role,omitempty"`
}

func (UserRole) TableName() string {
	return "user_roles"
}

// ============================================
// 请求和响应结构
// ============================================

// CreateRoleRequest 创建角色请求
type CreateRoleRequest struct {
	Name        string `json:"name" binding:"required,min=2,max=50"`
	DisplayName string `json:"display_name" binding:"required,min=2,max=100"`
	Description string `json:"description"`
}

// UpdateRoleRequest 更新角色请求
type UpdateRoleRequest struct {
	DisplayName string `json:"display_name" binding:"required,min=2,max=100"`
	Description string `json:"description"`
}

// AssignPermissionsRequest 分配权限请求
type AssignPermissionsRequest struct {
	PermissionIDs []uint `json:"permission_ids" binding:"required"`
}

// AssignRoleRequest 分配角色请求
type AssignRoleRequest struct {
	RoleID uint `json:"role_id" binding:"required"`
}

// RoleResponse 角色响应
type RoleResponse struct {
	ID          uint                 `json:"id"`
	Name        string               `json:"name"`
	DisplayName string               `json:"display_name"`
	Description string               `json:"description"`
	IsSystem    bool                 `json:"is_system"`
	CreatedAt   time.Time            `json:"created_at"`
	UpdatedAt   time.Time            `json:"updated_at"`
	Permissions []PermissionResponse `json:"permissions,omitempty"`
}

// RoleListItem 角色列表项
type RoleListItem struct {
	ID              uint      `json:"id"`
	Name            string    `json:"name"`
	DisplayName     string    `json:"display_name"`
	Description     string    `json:"description"`
	IsSystem        bool      `json:"is_system"`
	PermissionCount int       `json:"permission_count"`
	CreatedAt       time.Time `json:"created_at"`
}
