package models

import (
	"time"
)

type CheckinRecord struct {
	ID          uint      `gorm:"primaryKey;column:id" json:"id"`
	UserID      uint      `gorm:"column:user_id;not null" json:"user_id"`
	LocationID  uint      `gorm:"column:location_id;not null" json:"location_id"`
	CheckinTime time.Time `gorm:"column:checkin_time;autoCreateTime" json:"checkin_time"`
	Notes       string    `gorm:"column:notes" json:"notes"`
	Images      string    `gorm:"column:images;type:text" json:"images"` // JSON字符串存储图片URL数组
	CreatedAt   time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
}

// 指定表名
func (CheckinRecord) TableName() string {
	return "checkin_records"
}

// 打卡请求
type CheckinRequest struct {
	LocationID uint     `json:"location_id" binding:"required"`
	Notes      string   `json:"notes"`
	Images     []string `json:"images"` // 图片URL数组
}

// 打卡响应
type CheckinResponse struct {
	ID          uint      `json:"id"`
	UserID      uint      `json:"user_id"`
	LocationID  uint      `json:"location_id"`
	CheckinTime time.Time `json:"checkin_time"`
	Notes       string    `json:"notes"`
	Images      []string  `json:"images"`
	CreatedAt   time.Time `json:"created_at"`

	// 关联信息
	LocationName string `json:"location_name"`
	UserName     string `json:"user_name"`
}

// 用户打卡统计
type UserCheckinStats struct {
	TotalCheckins  int       `json:"total_checkins"`
	ContinuousDays int       `json:"continuous_days"`
	LastCheckin    time.Time `json:"last_checkin"`
	LocationsCount int       `json:"locations_count"` // 打卡过的不同地点数量
}
