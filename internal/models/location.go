package models

import (
	"time"
)

type Location struct {
	ID        uint      `gorm:"primaryKey;column:id" json:"id"`
	Name      string    `gorm:"column:name;not null" json:"name"`
	Address   string    `gorm:"column:address" json:"address"`
	Latitude  float64   `gorm:"column:latitude;type:decimal(10,6);not null" json:"latitude"`
	Longitude float64   `gorm:"column:longitude;type:decimal(10,6);not null" json:"longitude"`
	Type      string    `gorm:"column:type" json:"type"`       // scenic, cultural, museum
	ItemID    uint      `gorm:"column:item_id" json:"item_id"` // 关联非遗项目
	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
}

// 指定表名
func (Location) TableName() string {
	return "locations"
}

// 创建地理位置请求
type CreateLocationRequest struct {
	Name      string  `json:"name" binding:"required"`
	Address   string  `json:"address"`
	Latitude  float64 `json:"latitude" binding:"required"`
	Longitude float64 `json:"longitude" binding:"required"`
	Type      string  `json:"type"`
	ItemID    uint    `json:"item_id"` // 关联的非遗项目ID
}

// 地理位置响应
type LocationResponse struct {
	ID        uint      `json:"id"`
	Name      string    `json:"name"`
	Address   string    `json:"address"`
	Latitude  float64   `json:"latitude"`
	Longitude float64   `json:"longitude"`
	Type      string    `json:"type"`
	ItemID    uint      `json:"item_id"`
	CreatedAt time.Time `json:"created_at"`
}
