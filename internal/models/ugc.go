package models

import (
	"time"
)

type UGCContent struct {
	ID         uint      `gorm:"primaryKey;column:id" json:"id"`
	UserID     uint      `gorm:"column:user_id;not null" json:"user_id"`
	Title      string    `gorm:"column:title" json:"title"`
	Content    string    `gorm:"column:content" json:"content"`
	Type       string    `gorm:"column:type" json:"type"`                       // article, photo, video
	MediaURLs  string    `gorm:"column:media_urls;type:text" json:"media_urls"` // JSON字符串存储媒体URL数组
	LocationID uint      `gorm:"column:location_id" json:"location_id"`
	Status     string    `gorm:"column:status;default:pending" json:"status"` // pending, approved, rejected
	CreatedAt  time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
}

// 指定表名
func (UGCContent) TableName() string {
	return "ugc_contents"
}

// 创建UGC内容请求
type CreateUGCRequest struct {
	Title      string `json:"title" binding:"required"`
	Content    string `json:"content"`
	Type       string `json:"type" binding:"required"`                       // article, photo, video
	MediaURLs  string `gorm:"column:media_urls;type:text" json:"media_urls"` // JSON字符串存储媒体URL数组
	LocationID uint   `json:"location_id"`
}

// 更新UGC内容请求
type UpdateUGCRequest struct {
	Title   string `json:"title"`
	Content string `json:"content"`
	Status  string `json:"status"` // 管理员审核用
}

// UGC内容响应
type UGCResponse struct {
	ID         uint      `json:"id"`
	UserID     uint      `json:"user_id"`
	Title      string    `json:"title"`
	Content    string    `json:"content"`
	Type       string    `json:"type"`
	MediaURLs  []string  `json:"media_urls"`
	LocationID uint      `json:"location_id"`
	Status     string    `json:"status"`
	CreatedAt  time.Time `json:"created_at"`

	// 关联信息
	UserName     string `json:"user_name"`
	UserAvatar   string `json:"user_avatar"`
	LocationName string `json:"location_name"`
}

// UGC内容统计
type UGCStats struct {
	TotalContents int `json:"total_contents"`
	ArticlesCount int `json:"articles_count"`
	PhotosCount   int `json:"photos_count"`
	VideosCount   int `json:"videos_count"`
	ApprovedCount int `json:"approved_count"`
	PendingCount  int `json:"pending_count"`
}
