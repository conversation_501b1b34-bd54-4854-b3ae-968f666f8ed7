package models

import (
	"time"
)

type FileRecord struct {
	ID         uint      `gorm:"primaryKey;column:id" json:"id"`
	UserID     uint      `gorm:"column:user_id;not null" json:"user_id"`
	Filename   string    `gorm:"column:filename;not null" json:"filename"`
	FileURL    string    `gorm:"column:file_url;not null" json:"file_url"`
	FileSize   int64     `gorm:"column:file_size" json:"file_size"`
	FileType   string    `gorm:"column:file_type" json:"file_type"`
	UploadType string    `gorm:"column:upload_type;not null" json:"upload_type"` // avatar, ugc, checkin
	TargetID   uint      `gorm:"column:target_id" json:"target_id"`
	CreatedAt  time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
}

// TableName 指定表名
func (FileRecord) TableName() string {
	return "file_records"
}

// FileUploadRequest 文件上传请求
type FileUploadRequest struct {
	UploadType string `form:"upload_type" binding:"required,oneof=avatar ugc checkin"`
	TargetID   uint   `form:"target_id"` // 可选，关联目标ID
}

// FileUploadResponse 文件上传响应
type FileUploadResponse struct {
	ID         uint      `json:"id"`
	Filename   string    `json:"filename"`
	FileURL    string    `json:"file_url"`
	FileSize   int64     `json:"file_size"`
	FileType   string    `json:"file_type"`
	UploadType string    `json:"upload_type"`
	TargetID   uint      `json:"target_id"`
	CreatedAt  time.Time `json:"created_at"`
}

// FileListResponse 文件列表响应
type FileListResponse struct {
	ID         uint      `json:"id"`
	Filename   string    `json:"filename"`
	FileURL    string    `json:"file_url"`
	FileSize   int64     `json:"file_size"`
	FileType   string    `json:"file_type"`
	UploadType string    `json:"upload_type"`
	TargetID   uint      `json:"target_id"`
	CreatedAt  time.Time `json:"created_at"`

	// 关联信息
	UserName string `json:"user_name"`
}
