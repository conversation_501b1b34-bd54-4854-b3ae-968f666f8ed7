package models

import (
	"time"
)

// ============================================
// 非遗传承人认证模型
// ============================================

// HeritagePersonApplication 非遗传承人认证申请
type HeritagePersonApplication struct {
	ID                   uint       `gorm:"primaryKey;column:id" json:"id"`
	UserID               uint       `gorm:"column:user_id;uniqueIndex;not null" json:"user_id"`
	RealName             string     `gorm:"column:real_name;not null" json:"real_name"`
	IDCard               string     `gorm:"column:id_card;not null" json:"id_card"`
	HeritageCategory     string     `gorm:"column:heritage_category" json:"heritage_category"`
	HeritageItemID       *uint      `gorm:"column:heritage_item_id" json:"heritage_item_id"`
	CertificationLevel   string     `gorm:"column:certification_level" json:"certification_level"`
	CertificationNumber  string     `gorm:"column:certification_number" json:"certification_number"`
	CertificationFiles   string     `gorm:"column:certification_files" json:"certification_files"`
	ExpertiseDescription string     `gorm:"column:expertise_description" json:"expertise_description"`
	ApplicationStatus    string     `gorm:"column:application_status;default:pending" json:"application_status"`
	AppliedAt            time.Time  `gorm:"column:applied_at;autoCreateTime" json:"applied_at"`
	ReviewedAt           *time.Time `gorm:"column:reviewed_at" json:"reviewed_at"`
	ReviewerID           *uint      `gorm:"column:reviewer_id" json:"reviewer_id"`
	ReviewComment        string     `gorm:"column:review_comment" json:"review_comment"`
	CreatedAt            time.Time  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt            time.Time  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

func (HeritagePersonApplication) TableName() string {
	return "heritage_person_applications"
}

// ============================================
// 请求和响应结构
// ============================================

// SubmitApplicationRequest 提交认证申请请求
type SubmitApplicationRequest struct {
	RealName             string `json:"real_name" binding:"required,min=2,max=100"`
	IDCard               string `json:"id_card" binding:"required,len=18"`
	HeritageCategory     string `json:"heritage_category"`
	HeritageItemID       *uint  `json:"heritage_item_id"`
	CertificationLevel   string `json:"certification_level"`
	CertificationNumber  string `json:"certification_number"`
	CertificationFiles   string `json:"certification_files"`
	ExpertiseDescription string `json:"expertise_description" binding:"required,min=10"`
}

// ReviewApplicationRequest 审核认证申请请求
type ReviewApplicationRequest struct {
	Status        string `json:"status" binding:"required,oneof=approved rejected"`
	ReviewComment string `json:"review_comment"`
}

// ApplicationResponse 认证申请响应
type ApplicationResponse struct {
	ID                   uint       `json:"id"`
	UserID               uint       `json:"user_id"`
	Username             string     `json:"username"`
	RealName             string     `json:"real_name"`
	IDCard               string     `json:"id_card"`
	HeritageCategory     string     `json:"heritage_category"`
	HeritageItemID       *uint      `json:"heritage_item_id"`
	HeritageItemName     string     `json:"heritage_item_name,omitempty"`
	CertificationLevel   string     `json:"certification_level"`
	CertificationNumber  string     `json:"certification_number"`
	CertificationFiles   string     `json:"certification_files"`
	ExpertiseDescription string     `json:"expertise_description"`
	ApplicationStatus    string     `json:"application_status"`
	AppliedAt            time.Time  `json:"applied_at"`
	ReviewedAt           *time.Time `json:"reviewed_at"`
	ReviewerID           *uint      `json:"reviewer_id"`
	ReviewerName         string     `json:"reviewer_name,omitempty"`
	ReviewComment        string     `json:"review_comment"`
	CreatedAt            time.Time  `json:"created_at"`
	UpdatedAt            time.Time  `json:"updated_at"`
}

// ApplicationListItem 认证申请列表项
type ApplicationListItem struct {
	ID                uint      `json:"id"`
	UserID            uint      `json:"user_id"`
	Username          string    `json:"username"`
	RealName          string    `json:"real_name"`
	HeritageCategory  string    `json:"heritage_category"`
	ApplicationStatus string    `json:"application_status"`
	AppliedAt         time.Time `json:"applied_at"`
}
