package models

import (
	"time"
)

type UserRelationship struct {
	ID          uint      `gorm:"primaryKey;column:id" json:"id"`
	FollowerID  uint      `gorm:"column:follower_id;not null" json:"follower_id"`
	FollowingID uint      `gorm:"column:following_id;not null" json:"following_id"`
	CreatedAt   time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
}

// 指定表名
func (UserRelationship) TableName() string {
	return "user_relationships"
}

// 关注请求
type FollowRequest struct {
	FollowingID uint `json:"following_id" binding:"required"`
}

// 用户关系响应
type RelationshipResponse struct {
	ID          uint      `json:"id"`
	FollowerID  uint      `json:"follower_id"`
	FollowingID uint      `json:"following_id"`
	CreatedAt   time.Time `json:"created_at"`

	// 关联信息
	FollowerUsername  string `json:"follower_username"`
	FollowerAvatar    string `json:"follower_avatar"`
	FollowingUsername string `json:"following_username"`
	FollowingAvatar   string `json:"following_avatar"`
}

// 用户统计信息
type UserStats struct {
	UserID         uint   `json:"user_id"`
	Username       string `json:"username"`
	AvatarURL      string `json:"avatar_url"`
	PostsCount     int    `json:"posts_count"`     // 发布内容数
	CheckinsCount  int    `json:"checkins_count"`  // 打卡次数
	FollowersCount int    `json:"followers_count"` // 粉丝数
	FollowingCount int    `json:"following_count"` // 关注数
	IsFollowing    bool   `json:"is_following"`    // 当前用户是否关注此用户
	IsFollowed     bool   `json:"is_followed"`     // 此用户是否关注当前用户
}

// 用户主页信息
type UserProfile struct {
	UserID    uint      `json:"user_id"`
	Username  string    `json:"username"`
	Email     string    `json:"email"`
	AvatarURL string    `json:"avatar_url"`
	Role      string    `json:"role"`
	CreatedAt time.Time `json:"created_at"`

	// 统计信息
	PostsCount     int `json:"posts_count"`
	CheckinsCount  int `json:"checkins_count"`
	FollowersCount int `json:"followers_count"`
	FollowingCount int `json:"following_count"`

	// 关系状态
	IsFollowing bool `json:"is_following"`
	IsFollowed  bool `json:"is_followed"`
	IsSelf      bool `json:"is_self"` // 是否是当前用户自己
}
