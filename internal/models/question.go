package models

import (
	"time"

	"gorm.io/gorm"
)

// Question 问题模型
type Question struct {
	ID            uint           `gorm:"primaryKey" json:"id"`
	UserID        uint           `gorm:"not null;index" json:"user_id"`
	Title         string         `gorm:"type:varchar(200);not null" json:"title"`
	Content       string         `gorm:"type:text;not null" json:"content"`
	Category      string         `gorm:"type:varchar(50);index" json:"category"`
	Tags          string         `gorm:"type:varchar(200)" json:"tags"`
	Status        string         `gorm:"type:varchar(20);default:'pending';index" json:"status"` // pending-待解决, solved-已解决
	BestAnswerID  *uint          `gorm:"index" json:"best_answer_id"`
	ViewCount     int            `gorm:"default:0" json:"view_count"`
	AnswerCount   int            `gorm:"default:0" json:"answer_count"`
	FollowCount   int            `gorm:"default:0" json:"follow_count"`
	LikeCount     int            `gorm:"default:0" json:"like_count"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 关联
	User       User      `gorm:"foreignKey:UserID" json:"user,omitempty"`
	BestAnswer *Answer   `gorm:"foreignKey:BestAnswerID" json:"best_answer,omitempty"`
	Answers    []Answer  `gorm:"foreignKey:QuestionID" json:"answers,omitempty"`
	Followers  []User    `gorm:"many2many:question_follows;" json:"followers,omitempty"`
}

// TableName 指定表名
func (Question) TableName() string {
	return "questions"
}

// Answer 答案模型
type Answer struct {
	ID         uint           `gorm:"primaryKey" json:"id"`
	QuestionID uint           `gorm:"not null;index" json:"question_id"`
	UserID     uint           `gorm:"not null;index" json:"user_id"`
	Content    string         `gorm:"type:text;not null" json:"content"`
	IsBest     bool           `gorm:"default:false;index" json:"is_best"`
	LikeCount  int            `gorm:"default:0" json:"like_count"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 关联
	Question Question `gorm:"foreignKey:QuestionID" json:"question,omitempty"`
	User     User     `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// TableName 指定表名
func (Answer) TableName() string {
	return "answers"
}

// QuestionFollow 问题关注模型
type QuestionFollow struct {
	ID         uint      `gorm:"primaryKey" json:"id"`
	QuestionID uint      `gorm:"not null;index;uniqueIndex:idx_question_user" json:"question_id"`
	UserID     uint      `gorm:"not null;index;uniqueIndex:idx_question_user" json:"user_id"`
	CreatedAt  time.Time `json:"created_at"`

	// 关联
	Question Question `gorm:"foreignKey:QuestionID" json:"question,omitempty"`
	User     User     `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// TableName 指定表名
func (QuestionFollow) TableName() string {
	return "question_follows"
}

