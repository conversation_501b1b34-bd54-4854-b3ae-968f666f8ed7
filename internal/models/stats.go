package models

import (
	"time"
)

// 平台总数据统计
type PlatformStats struct {
	TotalUsers      int `json:"total_users"`       // 总用户数
	TotalHeritage   int `json:"total_heritage"`    // 总非遗项目数
	TotalUGC        int `json:"total_ugc"`         // 总UGC内容数
	TotalCheckins   int `json:"total_checkins"`    // 总打卡次数
	TotalComments   int `json:"total_comments"`    // 总评论数
	TotalLikes      int `json:"total_likes"`       // 总点赞数
	ActiveUsers     int `json:"active_users"`      // 活跃用户数（最近30天）
	NewUsersToday   int `json:"new_users_today"`   // 今日新增用户
	NewContentToday int `json:"new_content_today"` // 今日新增内容
}

// 用户增长趋势
type UserGrowth struct {
	Date  string `json:"date"`  // 日期
	Count int    `json:"count"` // 用户数
}

// 内容热度排行
type ContentRanking struct {
	ID           uint      `json:"id"`
	Title        string    `json:"title"`
	Type         string    `json:"type"`          // ugc, heritage_item
	AuthorName   string    `json:"author_name"`   // 作者名
	ViewCount    int       `json:"view_count"`    // 浏览量
	LikeCount    int       `json:"like_count"`    // 点赞数
	CommentCount int       `json:"comment_count"` // 评论数
	HotScore     float64   `json:"hot_score"`     // 热度分数
	CreatedAt    time.Time `json:"created_at"`
}

// 打卡地点热度
type LocationHotness struct {
	LocationID   uint   `json:"location_id"`
	LocationName string `json:"location_name"`
	CheckinCount int    `json:"checkin_count"` // 打卡次数
	UserCount    int    `json:"user_count"`    // 打卡用户数
	HotScore     int    `json:"hot_score"`     // 热度分数
}

// 用户行为分析
type UserBehavior struct {
	Date         string  `json:"date"`
	ActiveUsers  int     `json:"active_users"`  // 日活用户
	NewUsers     int     `json:"new_users"`     // 新增用户
	CheckinCount int     `json:"checkin_count"` // 打卡次数
	ContentCount int     `json:"content_count"` // 内容发布数
	CommentCount int     `json:"comment_count"` // 评论数
	AvgSession   float64 `json:"avg_session"`   // 平均会话时长（分钟）
}

// 运营数据报表
type OperationReport struct {
	Period        string            `json:"period"`         // 时间段
	UserGrowth    float64           `json:"user_growth"`    // 用户增长率
	ContentGrowth float64           `json:"content_growth"` // 内容增长率
	Engagement    float64           `json:"engagement"`     // 用户参与度
	Retention     float64           `json:"retention"`      // 用户留存率
	TopContent    []ContentRanking  `json:"top_content"`    // 热门内容
	TopLocations  []LocationHotness `json:"top_locations"`  // 热门地点
}
