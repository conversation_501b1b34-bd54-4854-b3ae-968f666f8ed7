package models

import (
	"time"
)

type Comment struct {
	ID         uint      `gorm:"primaryKey;column:id" json:"id"`
	UserID     uint      `gorm:"column:user_id;not null" json:"user_id"`
	Content    string    `gorm:"column:content;not null" json:"content"`
	TargetType string    `gorm:"column:target_type;not null" json:"target_type"`
	TargetID   uint      `gorm:"column:target_id;not null" json:"target_id"`
	ParentID   *uint     `gorm:"column:parent_id" json:"parent_id"` // 使用指针，允许NULL
	LikeCount  int       `gorm:"column:like_count;default:0" json:"like_count"`
	Status     string    `gorm:"column:status;default:approved" json:"status"`
	CreatedAt  time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
}

// 指定表名
func (Comment) TableName() string {
	return "comments"
}

// 点赞记录
type Like struct {
	ID         uint      `gorm:"primaryKey;column:id" json:"id"`
	UserID     uint      `gorm:"column:user_id;not null" json:"user_id"`
	TargetType string    `gorm:"column:target_type;not null" json:"target_type"` // ugc, comment
	TargetID   uint      `gorm:"column:target_id;not null" json:"target_id"`
	CreatedAt  time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
}

// 指定表名
func (Like) TableName() string {
	return "likes"
}

// 收藏记录
type Collection struct {
	ID         uint      `gorm:"primaryKey;column:id" json:"id"`
	UserID     uint      `gorm:"column:user_id;not null" json:"user_id"`
	TargetType string    `gorm:"column:target_type;not null" json:"target_type"` // ugc, heritage_item
	TargetID   uint      `gorm:"column:target_id;not null" json:"target_id"`
	CreatedAt  time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
}

// 指定表名
func (Collection) TableName() string {
	return "collections"
}

// 创建评论请求
type CreateCommentRequest struct {
	Content    string `json:"content" binding:"required,min=1,max=1000"`
	TargetType string `json:"target_type" binding:"required,oneof=ugc heritage_item"`
	TargetID   uint   `json:"target_id" binding:"required"`
	ParentID   uint   `json:"parent_id"` // 可选，回复评论时使用
}

// 评论响应
type CommentResponse struct {
	ID         uint      `json:"id"`
	UserID     uint      `json:"user_id"`
	Content    string    `json:"content"`
	TargetType string    `json:"target_type"`
	TargetID   uint      `json:"target_id"`
	ParentID   uint      `json:"parent_id"`
	LikeCount  int       `json:"like_count"`
	Status     string    `json:"status"`
	CreatedAt  time.Time `json:"created_at"`

	// 关联信息
	UserName   string `json:"user_name"`
	UserAvatar string `json:"user_avatar"`
	Liked      bool   `json:"liked"` // 当前用户是否点赞

	// 子评论（用于嵌套显示）
	Replies []CommentResponse `json:"replies,omitempty"`
}

// 点赞请求
type LikeRequest struct {
	TargetType string `json:"target_type" binding:"required,oneof=ugc comment"`
	TargetID   uint   `json:"target_id" binding:"required"`
}

// 收藏请求
type CollectRequest struct {
	TargetType string `json:"target_type" binding:"required,oneof=ugc heritage_item"`
	TargetID   uint   `json:"target_id" binding:"required"`
}

// 收藏响应
type CollectionResponse struct {
	ID         uint      `json:"id"`
	UserID     uint      `json:"user_id"`
	TargetType string    `json:"target_type"`
	TargetID   uint      `json:"target_id"`
	CreatedAt  time.Time `json:"created_at"`

	// 关联信息
	Title      string `json:"title"` // 收藏内容的标题
	CoverImage string `json:"cover_image"`
}
