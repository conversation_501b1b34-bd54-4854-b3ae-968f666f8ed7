package models

import (
	"time"
)

type HeritageItem struct {
	ID          uint      `gorm:"primaryKey;column:id" json:"id"`
	Name        string    `gorm:"column:name;not null" json:"name"`
	Description string    `gorm:"column:description" json:"description"`
	Category    string    `gorm:"column:category" json:"category"`
	Location    string    `gorm:"column:location" json:"location"`
	Latitude    float64   `gorm:"column:latitude;type:decimal(10,6)" json:"latitude"`
	Longitude   float64   `gorm:"column:longitude;type:decimal(10,6)" json:"longitude"`
	CoverImage  string    `gorm:"column:cover_image" json:"cover_image"`
	Status      string    `gorm:"column:status;default:pending" json:"status"` // pending, approved, rejected
	CreatedBy   uint      `gorm:"column:created_by" json:"created_by"`
	CreatedAt   time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
}

// 指定表名
func (HeritageItem) TableName() string {
	return "heritage_items"
}

// 创建非遗项目请求
type CreateHeritageItemRequest struct {
	Name        string  `json:"name" binding:"required"`
	Description string  `json:"description"`
	Category    string  `json:"category"`
	Location    string  `json:"location"`
	Latitude    float64 `json:"latitude" binding:"required"`
	Longitude   float64 `json:"longitude" binding:"required"`
	CoverImage  string  `json:"cover_image"`
}

// 更新非遗项目请求
type UpdateHeritageItemRequest struct {
	Name        string  `json:"name"`
	Description string  `json:"description"`
	Category    string  `json:"category"`
	Location    string  `json:"location"`
	Latitude    float64 `json:"latitude"`
	Longitude   float64 `json:"longitude"`
	CoverImage  string  `json:"cover_image"`
	Status      string  `json:"status"`
}

// 非遗项目响应
type HeritageItemResponse struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Category    string    `json:"category"`
	Location    string    `json:"location"`
	Latitude    float64   `json:"latitude"`
	Longitude   float64   `json:"longitude"`
	CoverImage  string    `json:"cover_image"`
	Status      string    `json:"status"`
	ViewCount   int       `json:"view_count,omitempty"`
	CreatedBy   uint      `json:"created_by"`
	CreatedAt   time.Time `json:"created_at"`
}
