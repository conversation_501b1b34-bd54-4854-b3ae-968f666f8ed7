package models

import (
	"time"

	"gorm.io/gorm"
)

// PointRule 积分规则
type PointRule struct {
	ID          uint           `gorm:"primaryKey" json:"id"`
	ActionType  string         `gorm:"column:action_type;type:varchar(50);uniqueIndex;not null" json:"action_type"`
	ActionName  string         `gorm:"column:action_name;type:varchar(100);not null" json:"action_name"`
	Description string         `gorm:"type:text" json:"description"`
	Points      int            `gorm:"not null" json:"points"`
	DailyLimit  int            `gorm:"default:0" json:"daily_limit"`
	IsActive    bool           `gorm:"default:true" json:"is_active"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}

// PointRecord 积分记录
type PointRecord struct {
	ID            uint      `gorm:"primaryKey" json:"id"`
	UserID        uint      `gorm:"not null;index" json:"user_id"`
	ActionType    string    `gorm:"column:action_type;type:varchar(50);not null;index" json:"action_type"`
	Points        int       `gorm:"not null" json:"points"`
	Description   string    `gorm:"type:text" json:"description"`
	ReferenceType string    `gorm:"column:reference_type;type:varchar(50)" json:"reference_type"`
	ReferenceID   uint      `gorm:"column:reference_id" json:"reference_id"`
	CreatedAt     time.Time `json:"created_at"`

	// Associations
	User User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// Achievement 成就定义
type Achievement struct {
	ID             uint           `gorm:"primaryKey" json:"id"`
	Code           string         `gorm:"type:varchar(50);uniqueIndex;not null" json:"code"`
	Name           string         `gorm:"type:varchar(100);not null" json:"name"`
	Description    string         `gorm:"type:text" json:"description"`
	IconURL        string         `gorm:"column:icon_url;type:varchar(255)" json:"icon_url"`
	Category       string         `gorm:"type:varchar(50);index" json:"category"`
	ConditionType  string         `gorm:"column:condition_type;type:varchar(50);not null" json:"condition_type"`
	ConditionValue int            `gorm:"column:condition_value;not null" json:"condition_value"`
	RewardPoints   int            `gorm:"column:reward_points;default:0" json:"reward_points"`
	IsActive       bool           `gorm:"default:true" json:"is_active"`
	DisplayOrder   int            `gorm:"column:display_order;default:0" json:"display_order"`
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}

// UserAchievement 用户成就
type UserAchievement struct {
	ID            uint      `gorm:"primaryKey" json:"id"`
	UserID        uint      `gorm:"not null;index" json:"user_id"`
	AchievementID uint      `gorm:"not null;index" json:"achievement_id"`
	Progress      int       `gorm:"default:0" json:"progress"`
	IsCompleted   bool      `gorm:"default:false;index" json:"is_completed"`
	CompletedAt   *time.Time `json:"completed_at,omitempty"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`

	// Associations
	User        User        `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Achievement Achievement `gorm:"foreignKey:AchievementID" json:"achievement,omitempty"`
}

// UserLevel 用户等级
type UserLevel struct {
	ID                  uint      `gorm:"primaryKey" json:"id"`
	UserID              uint      `gorm:"uniqueIndex;not null" json:"user_id"`
	Level               int       `gorm:"default:1;index" json:"level"`
	TotalPoints         int       `gorm:"column:total_points;default:0" json:"total_points"`
	CurrentLevelPoints  int       `gorm:"column:current_level_points;default:0" json:"current_level_points"`
	NextLevelPoints     int       `gorm:"column:next_level_points;default:100" json:"next_level_points"`
	CreatedAt           time.Time `json:"created_at"`
	UpdatedAt           time.Time `json:"updated_at"`

	// Associations
	User User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// TableName 指定表名
func (PointRule) TableName() string {
	return "point_rules"
}

func (PointRecord) TableName() string {
	return "point_records"
}

func (Achievement) TableName() string {
	return "achievements"
}

func (UserAchievement) TableName() string {
	return "user_achievements"
}

func (UserLevel) TableName() string {
	return "user_levels"
}

