package models

import (
	"time"
)

// ============================================
// 权限模型
// ============================================

// Permission 权限
type Permission struct {
	ID          uint      `gorm:"primaryKey;column:id" json:"id"`
	Name        string    `gorm:"column:name;uniqueIndex;not null" json:"name"`
	DisplayName string    `gorm:"column:display_name;not null" json:"display_name"`
	Description string    `gorm:"column:description" json:"description"`
	Resource    string    `gorm:"column:resource;not null" json:"resource"`
	Action      string    `gorm:"column:action;not null" json:"action"`
	CreatedAt   time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`

	// 关联
	Roles []Role `gorm:"many2many:role_permissions;" json:"roles,omitempty"`
}

func (Permission) TableName() string {
	return "permissions"
}

// ============================================
// 角色权限关联
// ============================================

// RolePermission 角色权限关联
type RolePermission struct {
	ID           uint      `gorm:"primaryKey;column:id" json:"id"`
	RoleID       uint      `gorm:"column:role_id;not null" json:"role_id"`
	PermissionID uint      `gorm:"column:permission_id;not null" json:"permission_id"`
	CreatedAt    time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`

	// 关联
	Role       Role       `gorm:"foreignKey:RoleID" json:"role,omitempty"`
	Permission Permission `gorm:"foreignKey:PermissionID" json:"permission,omitempty"`
}

func (RolePermission) TableName() string {
	return "role_permissions"
}

// ============================================
// 请求和响应结构
// ============================================

// CreatePermissionRequest 创建权限请求
type CreatePermissionRequest struct {
	Name        string `json:"name" binding:"required,min=2,max=100"`
	DisplayName string `json:"display_name" binding:"required,min=2,max=100"`
	Description string `json:"description"`
	Resource    string `json:"resource" binding:"required,min=2,max=50"`
	Action      string `json:"action" binding:"required,min=2,max=50"`
}

// UpdatePermissionRequest 更新权限请求
type UpdatePermissionRequest struct {
	DisplayName string `json:"display_name" binding:"required,min=2,max=100"`
	Description string `json:"description"`
}

// PermissionResponse 权限响应
type PermissionResponse struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	DisplayName string    `json:"display_name"`
	Description string    `json:"description"`
	Resource    string    `json:"resource"`
	Action      string    `json:"action"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// PermissionListItem 权限列表项
type PermissionListItem struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	DisplayName string    `json:"display_name"`
	Resource    string    `json:"resource"`
	Action      string    `json:"action"`
	CreatedAt   time.Time `json:"created_at"`
}

// PermissionGroup 权限分组（按资源分组）
type PermissionGroup struct {
	Resource    string               `json:"resource"`
	Permissions []PermissionListItem `json:"permissions"`
}
