package main

import (
	"intangible_cultural_heritage_backend/internal/cache"
	"intangible_cultural_heritage_backend/internal/config"
	"intangible_cultural_heritage_backend/internal/database"
	"intangible_cultural_heritage_backend/internal/handlers"
	"intangible_cultural_heritage_backend/internal/middleware"
	"log"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	cfg := config.Load()

	// 初始化数据库
	if err := database.Init(cfg); err != nil {
		log.Fatal("Failed to initialize database:", err)
	}

	// 初始化Redis
	if err := cache.Init(cache.RedisConfig{
		Addr:     cfg.RedisAddr,
		Password: cfg.RedisPassword,
		DB:       cfg.RedisDB,
	}); err != nil {
		log.Printf("⚠️  Redis初始化失败: %v (将继续运行，但缓存功能不可用)", err)
	}

	// 设置Gin模式
	if cfg.ServerPort == "8080" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建Gin路由
	r := gin.Default()

	// 全局中间件
	r.Use(middleware.CORS())

	// 静态文件服务 - 提供上传文件的访问
	r.Static("/uploads", cfg.UploadPath)

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "Non-Heritage Platform API is running",
		})
	})

	// 初始化所有处理器
	userHandler := handlers.NewUserHandler()
	heritageHandler := handlers.NewHeritageHandler()
	locationHandler := handlers.NewLocationHandler()
	checkinHandler := handlers.NewCheckinHandler()
	ugcHandler := handlers.NewUGCHandler()
	commentHandler := handlers.NewCommentHandler()
	interactionHandler := handlers.NewInteractionHandler()
	fileHandler := handlers.NewFileHandler(cfg)
	relationshipHandler := handlers.NewRelationshipHandler()
	statsHandler := handlers.NewStatsHandler()
	heritagePersonHandler := handlers.NewHeritagePersonHandler()
	roleHandler := handlers.NewRoleHandler()
	permissionHandler := handlers.NewPermissionHandler()
	questionHandler := handlers.NewQuestionHandler()
	answerHandler := handlers.NewAnswerHandler()
	pointHandler := handlers.NewPointHandler()
	achievementHandler := handlers.NewAchievementHandler()

	api := r.Group("/api/v1")
	{
		// 公开路由
		api.POST("/register", userHandler.Register)
		api.POST("/login", userHandler.Login)
		api.GET("/heritage-items", heritageHandler.GetAllHeritageItems)
		api.GET("/heritage-items/:id", heritageHandler.GetHeritageItemByID)

		// 地理位置路由（公开）- PostGIS版本
		api.GET("/locations", locationHandler.GetLocationsList)           // 获取位置列表
		api.GET("/locations/:id", locationHandler.GetLocationByID)        // 获取位置详情
		api.GET("/locations/nearby", locationHandler.GetNearbyLocations)  // 附近位置查询（PostGIS）
		api.GET("/locations/popular", locationHandler.GetPopularLocations) // 热门地点
		api.POST("/locations/heatmap", locationHandler.GetHeatmapData)    // 热力图数据

		// UGC内容路由（公开）
		api.GET("/ugc", ugcHandler.GetAllUGC)
		api.GET("/ugc/type/:type", ugcHandler.GetUGCByType)

		// 评论路由（公开）
		api.GET("/comments/:target_type/:target_id", commentHandler.GetComments)

		// 用户信息路由（公开）
		api.GET("/users/:user_id/stats", relationshipHandler.GetUserStats)
		api.GET("/users/:user_id/profile", relationshipHandler.GetUserProfile)
		api.GET("/users/:user_id/followers", relationshipHandler.GetFollowers)
		api.GET("/users/:user_id/following", relationshipHandler.GetFollowing)

		// 问答路由（公开）
		api.GET("/questions", questionHandler.GetQuestionsList)       // 获取问题列表
		api.GET("/questions/:id", questionHandler.GetQuestionByID)    // 获取问题详情
		api.GET("/answers/question/:question_id", answerHandler.GetAnswersByQuestionID) // 获取问题的所有答案

		// 积分成就路由（公开）
		api.GET("/points/ranking", pointHandler.GetPointRanking)      // 积分排行榜
		api.GET("/points/rules", pointHandler.GetPointRules)          // 积分规则列表
		api.GET("/achievements", achievementHandler.GetAchievements)  // 成就列表

		// 需要认证的路由
		auth := api.Group("/auth")
		auth.Use(middleware.AuthMiddleware())
		{
			auth.GET("/profile", userHandler.GetProfile)
			auth.POST("/logout", userHandler.Logout)

			// 非遗项目管理
			heritage := auth.Group("/heritage-items")
			{
				heritage.POST("", heritageHandler.CreateHeritageItem)
				heritage.PUT("/:id", heritageHandler.UpdateHeritageItem)
			}

			// 地理位置管理（需要认证）
			auth.POST("/locations", locationHandler.CreateLocation)       // 创建位置
			auth.PUT("/locations/:id", locationHandler.UpdateLocation)    // 更新位置

			// 打卡功能
			checkin := auth.Group("/checkins")
			{
				checkin.POST("", checkinHandler.Checkin)
				checkin.GET("/my", checkinHandler.GetUserCheckins)
				checkin.GET("/stats", checkinHandler.GetUserCheckinStats)
				checkin.GET("/location/:location_id", checkinHandler.GetLocationCheckins)
				checkin.GET("/popular", checkinHandler.GetPopularLocations)
			}

			// UGC内容管理
			ugc := auth.Group("/ugc")
			{
				ugc.POST("", ugcHandler.CreateUGC)
				ugc.GET("/my", ugcHandler.GetUserUGC)
				ugc.PUT("/:id/review", ugcHandler.ReviewUGC)
				ugc.GET("/stats", ugcHandler.GetUGCStats)
			}

			// 评论系统
			comments := auth.Group("/comments")
			{
				comments.POST("", commentHandler.CreateComment)
				comments.DELETE("/:id", commentHandler.DeleteComment)
				comments.POST("/:id/like", commentHandler.LikeComment)
			}

			// 点赞收藏系统
			interaction := auth.Group("/interaction")
			{
				interaction.POST("/like", interactionHandler.LikeContent)
				interaction.POST("/collect", interactionHandler.CollectContent)
				interaction.GET("/my/collections", interactionHandler.GetMyCollections)
			}

			// 文件上传管理
			files := auth.Group("/files")
			{
				files.POST("/upload", fileHandler.UploadFile)
				files.GET("", fileHandler.GetFiles)
				files.DELETE("/:id", fileHandler.DeleteFile)
			}

			// 用户关系系统
			relationships := auth.Group("/relationships")
			{
				relationships.POST("/follow", relationshipHandler.FollowUser)
				relationships.DELETE("/unfollow/:user_id", relationshipHandler.UnfollowUser)
				relationships.GET("/check/:user_id", relationshipHandler.CheckFollowStatus)
			}

			// 非遗人认证系统
			heritagePerson := auth.Group("/heritage-people")
			{
				heritagePerson.POST("/apply", heritagePersonHandler.Apply)
				heritagePerson.GET("/my-application", heritagePersonHandler.GetMyApplication)
				heritagePerson.GET("/status", heritagePersonHandler.GetStatus)
			}

			// 问答系统（需要认证）
			questions := auth.Group("/questions")
			{
				questions.POST("", questionHandler.CreateQuestion)                    // 创建问题
				questions.GET("/my", questionHandler.GetMyQuestions)                  // 我的问题
				questions.PUT("/:id", questionHandler.UpdateQuestion)                 // 更新问题
				questions.DELETE("/:id", questionHandler.DeleteQuestion)              // 删除问题
				questions.POST("/:id/best-answer", questionHandler.SetBestAnswer)     // 设置最佳答案
				questions.POST("/:id/follow", questionHandler.FollowQuestion)         // 关注问题
				questions.DELETE("/:id/follow", questionHandler.UnfollowQuestion)     // 取消关注
				questions.GET("/followed", questionHandler.GetFollowedQuestions)      // 关注的问题
				questions.GET("/:id/follow-status", questionHandler.CheckFollowStatus) // 检查关注状态
			}

			// 答案系统（需要认证）
			answers := auth.Group("/answers")
			{
				answers.POST("", answerHandler.CreateAnswer)         // 创建答案
				answers.GET("/my", answerHandler.GetMyAnswers)       // 我的答案
				answers.GET("/:id", answerHandler.GetAnswerByID)     // 获取答案详情
				answers.PUT("/:id", answerHandler.UpdateAnswer)      // 更新答案
				answers.DELETE("/:id", answerHandler.DeleteAnswer)   // 删除答案
				answers.GET("/stats", answerHandler.GetAnswerStats)  // 答案统计
			}

			// 积分路由
			points := auth.Group("/points")
			{
				points.GET("/level", pointHandler.GetUserLevel)         // 获取用户等级
				points.GET("/records", pointHandler.GetPointRecords)    // 获取积分记录
				points.GET("/stats", pointHandler.GetUserPointStats)    // 获取积分统计
			}

			// 成就路由
			achievements := auth.Group("/achievements")
			{
				achievements.GET("/my", achievementHandler.GetUserAchievements)      // 获取用户成就
				achievements.GET("/stats", achievementHandler.GetUserAchievementStats) // 获取成就统计
			}

			// 管理员路由
			admin := auth.Group("/admin")
			admin.Use(middleware.AdminMiddleware())
			{
				// 非遗人认证审核
				admin.GET("/heritage-people/applications", heritagePersonHandler.GetApplications)
				admin.PUT("/heritage-people/review/:id", heritagePersonHandler.ReviewApplication)

				// 角色管理
				admin.POST("/roles", roleHandler.CreateRole)
				admin.GET("/roles", roleHandler.GetRoles)
				admin.GET("/roles/:id", roleHandler.GetRoleByID)
				admin.PUT("/roles/:id", roleHandler.UpdateRole)
				admin.DELETE("/roles/:id", roleHandler.DeleteRole)
				admin.POST("/roles/:id/permissions", roleHandler.AssignPermissions)

				// 权限管理
				admin.POST("/permissions", permissionHandler.CreatePermission)
				admin.GET("/permissions", permissionHandler.GetPermissions)
				admin.GET("/permissions/grouped", permissionHandler.GetPermissionsGrouped)
				admin.PUT("/permissions/:id", permissionHandler.UpdatePermission)
				admin.DELETE("/permissions/:id", permissionHandler.DeletePermission)

				// 地理位置管理（管理员）
				admin.DELETE("/locations/:id", locationHandler.DeleteLocation) // 删除位置

				// 积分规则管理
				admin.POST("/points/rules", pointHandler.CreatePointRule)       // 创建积分规则
				admin.PUT("/points/rules/:id", pointHandler.UpdatePointRule)    // 更新积分规则
				admin.DELETE("/points/rules/:id", pointHandler.DeletePointRule) // 删除积分规则

				// 成就管理
				admin.POST("/achievements", achievementHandler.CreateAchievement)       // 创建成就
				admin.PUT("/achievements/:id", achievementHandler.UpdateAchievement)    // 更新成就
				admin.DELETE("/achievements/:id", achievementHandler.DeleteAchievement) // 删除成就
			}

			// 数据统计系统（需要管理员权限）
			stats := auth.Group("/stats")
			stats.Use(middleware.AdminMiddleware())
			{
				stats.GET("/platform", statsHandler.GetPlatformStats)
				stats.GET("/content-ranking", statsHandler.GetContentRanking)
				stats.GET("/user-growth", statsHandler.GetUserGrowth)
				stats.GET("/user-behavior", statsHandler.GetUserBehavior)
				stats.GET("/hot-locations", statsHandler.GetHotLocations)
			}
		}
	}

	// 启动服务器
	log.Printf("Server starting on port %s", cfg.ServerPort)
	log.Printf("Upload directory: %s", cfg.UploadPath)
	if err := r.Run(":" + cfg.ServerPort); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
